'use client'

import { joi<PERSON><PERSON>olver } from '@hookform/resolvers/joi'
import { Add, Circle, Mic, VideoCall } from '@mui/icons-material'
import LoadingButton from '@mui/lab/LoadingButton'
import {
  Box,
  Button,
  CircularProgress,
  Divider,
  FormControl,
  IconButton,
  InputBase,
  LinearProgress,
  ListItem,
  MenuItem,
  Select,
  Typography,
} from '@mui/material'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Joi from 'joi'
import { uniqueId } from 'lodash'
import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'
import React, { useEffect, useRef, useState } from 'react'
import { Controller, useForm } from 'react-hook-form'

import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { SVGUpload } from '@memberup/shared/src/components/svgs/upload'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import {
  createL<PERSON>on<PERSON><PERSON>,
  createS<PERSON><PERSON><PERSON><PERSON>,
  deleteLesson<PERSON><PERSON>,
  deleteSection<PERSON>pi,
  getContentLibraryCourseApi,
  updateLessonApi,
  updateLibraryCourseApi,
  updateSectionApi,
} from '@memberup/shared/src/services/apis/content-library.api'
import { AUDIO_ACCEPT_ONLY } from '@memberup/shared/src/types/consts'
import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import AppAudioPlayer from '@/memberup/components/common/app-audio-player'
import AppTooltip from '@/memberup/components/common/app-tooltip'
import DesktopOnly from '@/memberup/components/common/desktop-only'
import AppTextEditor from '@/memberup/components/common/text-editor'
import AddLinkDialog from '@/memberup/components/dialogs/feed/edit-post-link-dialog'
import ResourceFileDialog from '@/memberup/components/dialogs/feed/resource-file-dialog'
import AppWarning from '@/memberup/components/dialogs/warning'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'
import FileAttachments from '@/memberup/components/list/file-attachments'
import CourseBuilderMenu from '@/memberup/components/menu/course-builder-menu'
import { StyledListItem, StyledMenu } from '@/memberup/components/styled/menu'
import SVGChevronDownLarge from '@/memberup/components/svgs/chevron-down-large'
import SVGCloseSmall from '@/memberup/components/svgs/close-small'
import SVGEye from '@/memberup/components/svgs/eyes'
import SVGTrash from '@/memberup/components/svgs/trash'
import SVGVisibilityOff from '@/memberup/components/svgs/visibility-off'
import ShowAudioMedia from '@/memberup/components/ui/show-audio-media'
import ShowVideoMedia from '@/memberup/components/ui/show-video-media'
import { getContentLibrary, selectContentLibrary } from '@/memberup/store/features/contentLibrarySlice'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'
import { getFileType } from '@/shared-libs/file'
import { capitalizeAllWords, shortenFileName } from '@/shared-libs/string-utils'
import { getMuxAssetApi, getMuxAssetsApi, getMuxTranscriptionApi } from '@/shared-services/apis/mux.api'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 8,
      maxWidth: 560,
      overflow: 'visible',
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
    '& .MuiDivider-root': {
      borderColor: theme.palette.action.disabledBackground,
    },
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.action.disabledBackground,
      borderRadius: 12,
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
  },
  buttons: {
    backgroundColor: 'transparent',
    borderRadius: 20,
    color: theme.palette.text.primary,
    borderColor: theme.palette.action.disabledBackground,
    borderStyle: 'solid',
    borderWidth: '1px',
    '&:hover': {
      backgroundColor: theme.palette.action.disabledBackground,
    },
  },
  mediaButtons: {
    borderRadius: '20px',
    backgroundColor: 'transparent',
    boxShadow: 'none',
    border: 'solid 1px',
    color: theme.palette.text.disabled,
    borderColor: theme.palette.action.disabledBackground,
    height: '56px',
    width: '200px',
    '&:hover': {
      backgroundColor: theme.palette.action.disabledBackground,
    },
  },
  mediaButtonIcon: {
    backgroundColor: theme.palette.action.disabledBackground,
    borderRadius: '50%',
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
  },
  dialogContent: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
  inputFields: {
    height: 40,
    '& .MuiInputBase-input': {
      boxSizing: 'border-box',
      height: '100%',
      fontFamily: 'Graphik Regular',
      fontSize: '14px',
    },
  },
  audioPlayerWrapper: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
}))

const textEditorModules = {
  toolbar: [
    [{ header: [1, 2, false] }],
    ['bold', 'italic', 'underline', 'strike'],
    [{ list: 'ordered' }, { list: 'bullet' }, { indent: '-1' }, { indent: '+1' }],
    ['link'],
  ],
}

const FormSchema = Joi.object({
  title: Joi.string().required().messages({
    'string.empty': `Title cannot be an empty field`,
  }),
}).options({ allowUnknown: true })
const RAW_FILE_MAX_SIZE_MB = 20
const VIDEO_FILE_MAX_SIZE_MB = 2000
const MEDIA_FILE_CHECK_INTERVAL = 5000

export default function CourseBuilderPage() {
  const dispatch = useAppDispatch()
  const [mediaType, setMediaType] = useState('text')
  const [autoSaving, setAutoSaving] = useState(false)
  const { contentLibrary: library } = useAppSelector((state) => selectContentLibrary(state))
  const router = useRouter()
  const params = useParams()
  const courseId = params.courseId
  const searchParams = useSearchParams()
  const lessonId = searchParams.get('lesson_id')
  const pathname = usePathname()
  const [sections, setSections] = useState({})
  const [selectedLessonId, setSelectedLessonId] = useState(null)
  const classes = useStyles()
  const [resourceFilesCount, setResourceFilesCount] = useState(0)
  const [courseSettings, setCourseSettings] = useState(null)
  const [isLoadingSections, setIsLoadingSections] = useState(false)
  const [uploadStatus, setUploadStatus] = useState(null)
  const { theme, isDarkTheme } = useAppTheme()
  const [isMobile, setIsMobile] = useState(true)
  const [newSectionId, setNewSectionId] = useState(null)
  const hasLessonBeenClickedRef = useRef(false)
  const {
    control: settingsControl,
    setValue: settingsSetValue,
    getValues: settingsGetValues,
    watch: settingsWatch,
    reset: settingsReset,
  } = useForm({
    mode: 'onBlur',
    reValidateMode: 'onBlur',
    resolver: joiResolver(FormSchema),
  })

  const descriptionValue = settingsWatch('description')
  const titleValue = settingsWatch('title')
  const [lastSavedSettings, setLastSavedSettings] = useState(null)
  const membership = useAppSelector((state) => selectMembership(state))
  const [selectedSectionId, setSelectedSectionId] = useState(null)
  const [hidenSidebar, setHidenSidebar] = useState(isMobile)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isCourseSettingsLoading, setIsCourseSettingsLoading] = useState(false)
  const [isVideoLoaded, setIsVideoLoaded] = useState(false)
  const [isAudioLoaded, setIsAudioLoaded] = useState(false)
  const [isCreatingNew, setIsCreatingNew] = useState(false)
  const [addResourcesAnchor, setAddResourcesAnchor] = useState(null)
  const savingLessonIdRef = useRef(null)
  const currentLessonIdRef = useRef(null)
  const uploadInitiatorLessonIdRef = useRef(null)
  const assetCheckIntervalRef = useRef(null)
  const resourceFileToBeDeletedRef = useRef(null)
  const isCreatingLesson = useRef(false)
  const lastSavedLessonMap = useRef({})
  const hideCourseUpdatedToast = useRef(false)
  const [showLinkDialog, setShowLinkDialog] = useState(false)
  const [showConfirmDeleteResourceDialog, setShowConfirmDeleteResourceDialog] = useState(false)
  const [initialQueryParamFetch, setInitialQueryParamFetch] = useState(false)
  const [currentLink, setCurrentLink] = useState({
    text: null,
    url: undefined,
    public_id: undefined,
  })

  const { uploadFiles, uploadProgress, uploadedFiles, initUploadFiles, handleUploadFiles } = useUploadFiles(
    'library',
    membership.id,
  )
  const { control, handleSubmit, formState, setValue, getValues, trigger, watch, reset } = useForm({
    mode: 'all',
    reValidateMode: 'onBlur',
    resolver: joiResolver(FormSchema),
    defaultValues: {
      visibility: 'draft',
      title: '',
      type: 'text',
      text: '',
      media_file: null,
      resource_files: [],
      sequence: 0,
      thumbnail_url: null,
      media_file_title: '',
      media_file_subtitle: '',
      media_transcript: null,
    },
  })
  const [disableLessonSwitch, setDisableLessonSwitch] = useState(false)
  const mediaFile = watch('media_file')
  const mediaTranscript = watch('media_transcript')
  const [open, setOpen] = useState(false)
  const [videoDownloadReady, setVideoDownloadReady] = useState(false)
  const [courseThumbnail, setCourseThumbnail] = useState(null)

  const [showResourceFileDialog, setShowResourceFileDialog] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [currentFile, setCurrentFile] = useState(null)
  const { isCurrentUserAdmin } = useCheckUserRole()
  const user = useStore((state) => state.auth.user)
  const userMembership = useStore((state) => state.community.userMembership)

  useEffect(() => {
    dispatch(getContentLibrary(membership.id))
    // Check the screen size and update showContent accordingly
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 600) // Adjust the width as needed
    }

    // Add an event listener for window resize
    window.addEventListener('resize', handleResize)

    // Call the handleResize function initially to set the initial state
    handleResize()

    // Clean up the event listener when the component unmounts
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [])

  useEffect(() => {
    if (courseSettings) {
      setLastSavedSettings(courseSettings)
    }
  }, [courseSettings])

  // useEffect(() => {
  //     let intervalId = null
  //     const staticRenditions = mediaFile?.static_renditions
  //     if (staticRenditions?.status === 'preparing') {
  //         console.error('renditions not ready')
  //         const muxUploadIdCurrentSession = uploadedFiles?.[0]?.mux_upload_id ?? false
  //
  //         intervalId = setInterval(async () => {
  //             console.error('transcription check interval')
  //             const result = await getMuxAssetsApi({
  //                 upload_id: mediaFile.upload_id,
  //             })
  //             const asset = result.data.data[0]
  //             if (asset.static_renditions?.status === 'ready') {
  //                 if (
  //                     currentLessonIdRef?.current ===
  //                     sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.id
  //                 ) {
  //                     const updatedMediaFile = {
  //                         ...mediaFile,
  //                         static_renditions: asset.static_renditions,
  //                     }
  //                     setVideoDownloadReady(true)
  //                     setValue('media_file', updatedMediaFile)
  //                 }
  //                 clearInterval(intervalId)
  //             }
  //         }, 3000)
  //     } else if (staticRenditions?.status === 'ready') {
  //         setVideoDownloadReady(true)
  //     }
  //
  //     return () => {
  //         if (intervalId) {
  //             clearInterval(intervalId)
  //         }
  //     }
  // }, [mediaFile])

  useEffect(() => {
    const initialFileCount = getValues('resource_files').length
    if (initialFileCount != resourceFilesCount) {
      setResourceFilesCount(initialFileCount)
    }
    currentLessonIdRef.current = selectedLessonId

    const section = sections?.[selectedSectionId]?.ContentLibraryCourseLesson
    if (section?.[selectedLessonId]?.type === 'video') {
      setMediaType('video')
      setValue('media_file', section?.[selectedLessonId]?.media_file)
    } else if (section?.[selectedLessonId]?.type === 'audio') {
      setMediaType('audio')
      setValue('media_file', section?.[selectedLessonId]?.media_file)
    } else {
      setMediaType('text')
      setValue('media_file', null)
    }
  }, [selectedLessonId])

  useEffect(() => {
    async function fetch() {
      try {
        setIsLoadingSections(true)
        await fetchCourseData(library.id, courseId)
        setIsLoadingSections(false)
      } catch (err) {
        setIsLoadingSections(false)
      }
    }

    if (library?.id && !initialQueryParamFetch && courseId && isCurrentUserAdmin) {
      setInitialQueryParamFetch(true)
      fetch()
    }
  }, [library, courseId])

  useEffect(() => {
    if (courseId && lessonId && user && userMembership.user_role === USER_ROLE_ENUM.member && sections) {
      const lessonVisibility = sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[lessonId]?.visibility
      const lessonQueryParam = `?lesson_id=${lessonId}`
      const redirectUrl =
        lessonQueryParam && lessonVisibility === 'published'
          ? `/course/${courseId}${lessonQueryParam}`
          : `/course/${courseId}`
      router.push(redirectUrl)
    }
  }, [user, sections, courseSettings])

  useEffect(() => {
    setHidenSidebar(isMobile)
  }, [isMobile])

  useEffect(() => {
    // Clear any existing interval
    if (assetCheckIntervalRef.current) {
      console.log('clearing interval as the media file has changed!')
      clearInterval(assetCheckIntervalRef.current)
    }
    if (mediaType !== 'video' || mediaFile?.status !== 'ready') {
      return
    }

    // We want to create an interval if the asset's subtitles or renditions (different versions of the video) are not ready.
    // TODO: Fix this!
    const subtitleTrack = mediaFile.tracks.find((track) => track.text_type === 'subtitles')
    if (subtitleTrack?.status === 'preparing' || mediaFile.static_renditions?.status === 'preparing') {
      setVideoDownloadReady(false)
      assetCheckIntervalRef.current = setInterval(async () => {
        const result = await getMuxAssetApi(mediaFile.id)
        const updatedAsset = result.data.data
        if (!updatedAsset || JSON.stringify(updatedAsset) === JSON.stringify(mediaFile)) {
          console.log(`No changes, try again later?`)
          return
        }

        let autoSave = false
        // Was the subtitle track 'changing' from preparing to 'ready'?
        const currentSubtitleTrack = mediaFile.tracks.find((track) => track.text_type === 'subtitles')
        const updatedSubtitleTrack = updatedAsset.tracks.find((track) => track.text_type === 'subtitles')
        if (
          currentSubtitleTrack &&
          currentSubtitleTrack.status === 'preparing' &&
          updatedSubtitleTrack?.status === 'ready' &&
          mediaTranscript === null
        ) {
          console.log('subtitle changed from preparing to ready, get the subtitle and persist')
          const generatedTranscript = await getMuxTranscriptionApi(mediaFile)
          let transcriptText = generatedTranscript?.data?.data
          if (
            transcriptText.includes('<Message>The specified key does not exist.</Message>') ||
            transcriptText.includes('<html>')
          ) {
          }
          setValue('media_transcript', transcriptText)
          autoSave = true
        }

        if (mediaFile.static_renditions?.status !== 'ready' && updatedAsset.static_renditions?.status === 'ready') {
          console.log('downloads are ready now!')
          autoSave = true
          setVideoDownloadReady(true)
        }

        if (autoSave) {
          const updatedMediaFile = {
            ...mediaFile,
            static_renditions: updatedAsset.static_renditions,
            tracks: updatedAsset.tracks,
          }
          setValue('media_file', updatedMediaFile)
          setAutoSaving(true)
          await saveChanges(getValues())
          setAutoSaving(false)
        }

        // Should stop checking for new changes?
        if (
          (!updatedSubtitleTrack || updatedSubtitleTrack?.status !== 'preparing') &&
          updatedAsset.static_renditions?.status !== 'preparing'
        ) {
          clearInterval(assetCheckIntervalRef.current)
          return
        }
      }, MEDIA_FILE_CHECK_INTERVAL)
    } else {
      setVideoDownloadReady(true)
      if (assetCheckIntervalRef.current) {
        clearInterval(assetCheckIntervalRef.current)
      }
    }

    return () => {
      if (assetCheckIntervalRef.current) {
        clearInterval(assetCheckIntervalRef.current)
      }
    }
  }, [mediaFile])

  /*useEffect(() => {
        let intervalId = null
        console.error('transcript mediaFile', mediaFile)

        if (mediaType !== 'video' || mediaFile?.status !== 'ready' || !['preparing', 'ready'].includes(mediaFile?.tracks?.find((track) => track.text_type === 'subtitles')?.status)) {
            return
        }

        intervalId = setInterval(async () => {
            // const uploadId = uploadedFiles?.[0]?.mux_upload_id || assetData?.mux_upload_id
            //     console.error('uploadId', uploadId)
            // const result = await getMuxAssetsApi({
            //   upload_id: uploadId
            // })
            // console.error('transcript result', result)
            // let asset = result.data.data[0]
            const transcriptionText = await getMuxTranscriptionApi(mediaFile)

            // TODO: Update the asset data, maybe we should just pool the asset data and check if the transcript is ready instead of querying for the transcription.




            console.error('transcription text', transcriptionText)

            const subtitlesTrack = mediaFile?.tracks?.find((track) => track.text_type === 'subtitles')
            if (
                subtitlesTrack &&
                (subtitlesTrack.status === 'ready' || subtitlesTrack.status === 'errored')
            ) {
                if (subtitlesTrack.status === 'errored') {
                    const noSoundOnVideoError = subtitlesTrack.error.messages.some(
                        (message) => message === 'Asset does not have an audio track'
                    )
                    const transcriptionText = noSoundOnVideoError
                        ? VIDEO_TRANSCRIPTION_NO_SOUND_ERROR_MSG
                        : VIDEO_TRANSCRIPTION_GENERAL_ERROR_MSG
                    setValue('media_transcript', transcriptionText)
                    await saveChanges(getValues())
                    clearInterval(intervalId)
                    return
                }
                let transcript = transcriptionText?.data?.data

                // TODO: Do not save a default text for transcript.

                if (
                    transcript.includes('<Message>The specified key does not exist.</Message>') ||
                    transcript.includes('<html>')
                ) {
                    transcript =
                        'Transcriptions are not ready yet. As soon as they are, you will see them here.'
                }

                if (currentLessonIdRef?.current === selectedLessonId) {
                    setValue('media_transcript', transcript)
                    await saveChanges(getValues())
                }
                clearInterval(intervalId)
            }
        }, TRANSCRIPTION_CHECK_INTERVAL)

        return () => {
            if (intervalId) {
                clearInterval(intervalId)
            }
        }
    }, [mediaFile])*/

  useEffect(() => {
    /* in case the user switches lessons, we need to make sure the lesson id that started the upload is the same as the current lesson id, otherwise we don't want to set the media_file value and other states */
    currentLessonIdRef.current = selectedLessonId
    let intervalId = null
    if (uploadProgress >= 100) {
      if (mediaType === 'video') {
        setUploadStatus('processing')
        intervalId = setInterval(async () => {
          const result = await getMuxAssetsApi({ upload_id: uploadedFiles[0].mux_upload_id })
          const asset = result.data.data[0]
          /*  const subtitlesTrack = asset?.tracks?.find((track) => track.text_type === 'subtitles') */
          if (
            asset.status === 'ready' /*
             subtitlesTrack &&
            (subtitlesTrack.status === 'ready' || subtitlesTrack.status === 'errored') */
          ) {
            if (uploadInitiatorLessonIdRef.current === selectedLessonId) {
              // const transcriptionText = await getMuxTranscriptionApi(asset)
              // let transcript = transcriptionText?.data?.data
              // if (!transcript.includes('<Message>The specified key does not exist.</Message>') && !transcript.includes('<html>')
              // ) {
              //     setValue('media_transcript', transcript)
              // }
              setValue('media_file', { ...uploadedFiles[0], ...uploadFiles[0], ...asset })
              setUploadStatus('ready')
              setHasUnsavedChanges(true)
              hideCourseUpdatedToast.current = false
              clearInterval(intervalId)
            } else {
              console.log('killing interval')
              clearInterval(intervalId)
            }
            setTimeout(() => setDisableLessonSwitch(false), 2000)
          }
        }, 3000)
      } else if (mediaType === 'audio') {
        if (currentLessonIdRef.current === selectedLessonId) {
          setValue('media_file', uploadedFiles[0] as any)
          setHasUnsavedChanges(true)
          hideCourseUpdatedToast.current = false
          setValue(
            'media_file_title',
            capitalizeAllWords(
              uploadedFiles?.[0]?.filename
                ?.split('.')
                ?.slice(0, -1)
                ?.join('.')
                ?.replaceAll('_', ' ')
                ?.replaceAll('-', ' ') || 'Audio Lesson',
            ),
          )
        } else {
          console.log('killing interval')
          clearInterval(intervalId)
        }
        setTimeout(() => setDisableLessonSwitch(false), 2000)
      }
    } else if (mediaType === 'audio') {
      if (currentLessonIdRef.current === selectedLessonId) {
        setIsAudioLoaded(true)
      } else {
        clearInterval(intervalId)
      }
      setTimeout(() => setDisableLessonSwitch(false), 2000)
    } else if (mediaType === 'video') {
      if (currentLessonIdRef.current === selectedLessonId) {
        setIsVideoLoaded(true)
      } else {
        clearInterval(intervalId)
      }
      setTimeout(() => setDisableLessonSwitch(false), 2000)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [uploadedFiles, uploadProgress])

  useEffect(() => {
    /* this is for when the add section button is pressed, so the addLesson will execute only when the new section id is ready since we are using optimistic ui */
    if (newSectionId) {
      addLesson(newSectionId)
    }
  }, [newSectionId])

  const {
    uploadProgress: resourceUploadProgress,
    handleUploadFiles: handleResourceUploadFiles,
    uploadedFiles: resourceUploadedFiles,
    initUploadFiles: initUploadResourceFiles,
    cancelUploads: cancelResourceUploads,
  } = useUploadFiles('library', membership.id)

  const updateCourseSettings = (courseData) => {
    const courseSettingsObj = {
      title: courseData?.title,
      description: courseData?.description,
      thumbnail: courseData?.thumbnail,
      visibility: courseData?.visibility,
    }
    setCourseSettings(courseSettingsObj)
  }
  const fetchCourseData = async (contentLibraryId, courseId) => {
    try {
      setIsCourseSettingsLoading(true)
      const response = await getContentLibraryCourseApi(contentLibraryId, courseId, membership.slug)
      const courseData = response.data

      if (courseData?.ContentLibraryCourseSection) {
        setSections(courseData.ContentLibraryCourseSection)
      }
      updateCourseSettings(courseData)
      return courseData?.ContentLibraryCourseSection
    } catch (error) {
      // Handle the error here
      console.log('Failed to fetch course data:', error)
    } finally {
      setIsCourseSettingsLoading(false)
      setIsCreatingNew(false)
    }
  }

  const handleFileSelect = async (file: File, fileTitle: string) => {
    const fileType = getFileType(file)
    const maxSize = fileType === 'video' ? VIDEO_FILE_MAX_SIZE_MB * 1024 * 1024 : RAW_FILE_MAX_SIZE_MB * 1024 * 1024

    if (file.size > maxSize) {
      toast.error(
        `The file size of ${shortenFileName(file.name, 8)} needs to be ${
          fileType === 'video' ? VIDEO_FILE_MAX_SIZE_MB : RAW_FILE_MAX_SIZE_MB
        } MB or less.`,
      )
      return
    }

    try {
      const uploadData = await handleResourceUploadFiles([file] as any, 'Cloudinary')
      if (uploadData[0]) {
        uploadData[0].title = fileTitle
        setCurrentFile(uploadData[0])
      }
      // Assuming handleResourceUploadFiles returns the file data you need to update the state
      setValue('resource_files', [...getValues('resource_files'), ...uploadData])
      setResourceFilesCount(getValues('resource_files').length)
      setHasUnsavedChanges(true)
      hideCourseUpdatedToast.current = false
    } catch (error) {
      console.error('Error uploading file:', error)
      toast.error('Error uploading file. Please try again.')
    }
  }

  const handleLinkDialogSave = ({ text, url, public_id }) => {
    let uniqueLinkId = null
    if (Boolean(public_id)) {
      // remove link with this id
      setValue(
        'resource_files',
        getValues('resource_files').filter((file) => file.public_id !== public_id),
      )
      uniqueLinkId = public_id
    } else {
      uniqueLinkId = Date.now().toString(36) + Math.random().toString(36).substring(2)
    }
    const resource = {
      filename: text,
      url: url,
      public_id: uniqueLinkId,
      type: 'link',
    }
    setValue('resource_files', [...getValues('resource_files'), resource])
    setShowLinkDialog(false)
    setAddResourcesAnchor(null)
    setCurrentLink(null)
    setHasUnsavedChanges(true)
    hideCourseUpdatedToast.current = false
    trigger()
  }

  const handleEditLink = (link) => {
    setCurrentLink(link)
    setShowLinkDialog(true)
  }

  const handleResourceFileDialogSave = async (file) => {
    // Assuming these parameters represent the updated file details

    const updatedFiles = getValues('resource_files').map((resource) => {
      if (resource.public_id === file.public_id) {
        return {
          ...(resourceUploadedFiles?.[0] || resource),
          title: file.title,
        }
      }
      return resource
    })

    setValue('resource_files', updatedFiles)

    setShowResourceFileDialog(false)
    setAddResourcesAnchor(null)
    setCurrentFile(null)

    /* save lesson */
    const lessonData = getValues()
    lessonData.resource_files = updatedFiles
    await onSubmit(lessonData)
  }

  const handleEditResourceFile = (fileData) => {
    const file = fileData.file
    file.text = file.filename
    setCurrentFile(file)
    setShowResourceFileDialog(true)
  }

  const addSection = async () => {
    setIsCreatingNew(true)
    if (hasUnsavedChanges) {
      const copiedFormValues = { ...getValues() }

      await saveChanges(copiedFormValues)

      toast.success('Course Updated')
    }

    const maxSequence = [...Object.values(sections)].map((section: any) => section.sequence).sort((a, b) => b - a)[0]

    const newSection = {
      name: 'New Section',
      content_library_id: library.id,
      content_library_course_id: courseId,
      sequence: maxSequence + 1,
      ContentLibraryCourseLesson: {},
    }

    // Optimistically update state
    const newOptimisticSectionId = uniqueId('section_')

    setSections((prevSections) => {
      const newSectionObjectWithOptimistic = {
        ...prevSections,
        [newOptimisticSectionId]: newSection,
      }

      return newSectionObjectWithOptimistic
    })

    try {
      const sectionResponse = await createSectionApi(courseId, newSection)
      const createdSection = sectionResponse.data

      // Update the local map state with the actual data returned from the API
      setSections((prevSections) => {
        const newSections = { ...prevSections }
        delete newSections[newOptimisticSectionId]
        newSections[createdSection.id] = createdSection
        setNewSectionId(createdSection.id)
        return newSections
      })
    } catch (error) {
      // If the API call fails, revert the change in the local state

      setSections((prevSections) => {
        const newSections = { ...prevSections }
        delete newSections[newOptimisticSectionId]
        return newSections
      })
      console.error('Failed to create section:', error)
    } finally {
      setIsCreatingNew(false)
    }
  }

  const deleteSection = async (sectionId) => {
    // Find the section to delete
    const sectionToDelete = sections[sectionId]
    // Optimistically update state
    setSections((prevSections) => {
      const newSections = { ...prevSections }
      delete newSections[sectionId]
      return newSections
    })
    try {
      /*  reset() */
      await deleteSectionApi(library.id, courseId, sectionId)
      const updatedSections = await fetchCourseData(library.id, courseId)
      // this is for when a section is deleted, the last lesson of the previous section is selected
      const previousSectionSequence = sectionToDelete.sequence - 1
      const lastSection: any =
        updatedSections &&
        Object.values(updatedSections).find((section: any) => section.sequence === previousSectionSequence)
      //select highest lesson sequence id
      const lastLesson: any =
        lastSection &&
        Object.values(lastSection.ContentLibraryCourseLesson).sort((a: any, b: any) => b.sequence - a.sequence)[0]

      if (lastSection && lastLesson) {
        selectLesson(lastSection?.id, lastLesson?.id)
      }
      toast.success('Section deleted successfully!')
    } catch (error) {
      // If the API call fails, revert the change in the local state
      /* setSections((prevSections) => [...prevSections, sectionToDelete]) */
      setSections((prevSections) => {
        const newSections = { ...prevSections }
        newSections[sectionId] = sectionToDelete
        return newSections
      })
      toast.error('Failed to delete section. Please try again.')
    }
  }

  const addLesson = async (sectionId: string) => {
    isCreatingLesson.current = true

    setIsCreatingNew(true)
    if (hasUnsavedChanges) {
      await saveChanges(getValues())
    }

    const section = sections[sectionId]
    let currentSequence = 1

    if (section && section.ContentLibraryCourseLesson) {
      const lastSequenceNumber = [...Object.values(section.ContentLibraryCourseLesson)]
        .map((lesson: any) => lesson.sequence)
        .sort((a, b) => b - a)[0]
      currentSequence = lastSequenceNumber + 1
    }
    const defaultLessonType = 'text'
    const newLesson = {
      visibility: 'draft',
      type: defaultLessonType,
      title: 'New Lesson',
      section_id: sectionId,
      content_library_course_id: courseId,
      sequence: currentSequence,
    }

    // Optimistically update state
    const optimisticTemporalId = uniqueId('lesson_')

    // Select the new lesson
    reset()
    setSelectedLessonId(optimisticTemporalId)
    setUploadStatus(null)
    setIsAudioLoaded(false)
    setIsVideoLoaded(false)
    setVideoDownloadReady(false)

    setSections((prevSections) => {
      const newSections = { ...prevSections }
      const section = newSections[sectionId]
      section.ContentLibraryCourseLesson = {
        ...section.ContentLibraryCourseLesson,
        [optimisticTemporalId]: newLesson,
      }
      return newSections
    })

    try {
      const contentLibraryId = library.id
      const lessonResponse = await createLessonApi(contentLibraryId, courseId, sectionId, newLesson, membership.id)
      const createdLesson = lessonResponse.data

      setSections((prevSections) => {
        const newSections = { ...prevSections }
        const section = newSections[sectionId]
        section.ContentLibraryCourseLesson = {
          ...section.ContentLibraryCourseLesson,
          [createdLesson.id]: createdLesson,
        }
        delete section.ContentLibraryCourseLesson[optimisticTemporalId]
        return newSections
      })

      // Select the new lesson with the real data
      setSelectedLessonId(createdLesson.id)
      setSelectedSectionId(sectionId)
      selectLesson(sectionId, createdLesson.id)
      /* we disable it here to avoid the scroll on lesson click and to keep the scroll on refresh page with query param and on add section/lesson*/
      hasLessonBeenClickedRef.current = false
      isCreatingLesson.current = false
    } catch (error) {
      // If the API call fails, revert the change in the local state

      setSections((prevSections) => {
        const newSections = { ...prevSections }
        const section = newSections[sectionId]
        delete section.ContentLibraryCourseLesson[optimisticTemporalId]
        return newSections
      })

      // Revert the selection if the lesson creation fails
      setSelectedLessonId(null)

      console.error('Failed to create lesson:', error)
    }

    setIsCreatingNew(false)
  }

  const downloadVideo = async (asset) => {
    let highestVideoQuality = null
    for (let file of asset.static_renditions.files) {
      if (file.name === 'medium.mp4' || (file.name === 'low.mp4' && !highestVideoQuality)) {
        highestVideoQuality = file
      }
    }

    if (highestVideoQuality) {
      // Create an AbortController instance
      const controller = new AbortController()
      const signal = controller.signal

      try {
        // Add the abort controller's signal to your fetch options
        const response = await fetch(
          `https://stream.mux.com/${asset.playback_ids?.[0]?.id}/${highestVideoQuality.name}`,
          { signal },
        )

        const contentLength = response.headers.get('Content-Length')
        let isLengthComputable = !!contentLength
        let total = isLengthComputable ? parseInt(contentLength, 10) : 0
        let loaded = 0

        // When creating the toast, save the toastId and the controller in the state
        // let toastId = toast.info('Starting download...', {
        //   style: {
        //     marginTop: '60px',
        //     right: '-25px',
        //   },
        //   autoClose: false,
        //   // Add a close button with an onClick handler that aborts the download
        //   closeButton: (
        //     <div
        //       onClick={() => {
        //         /* close toast */
        //         toast.dismiss(toastId)
        //         return controller.abort()
        //       }}
        //     >
        //       <SVGCloseSmall />
        //     </div>
        //   ),
        // })

        if (isLengthComputable && response.body) {
          const reader = response.body.getReader()
          let chunks = [] // Array to hold chunks of data
          let reading = true
          while (reading) {
            const { done, value } = await reader.read()
            if (done) {
              reading = false
            } else {
              loaded += value.byteLength
              chunks.push(value)
              // toast.update(toastId, {
              //   render: `Download progress: ${Math.round((loaded / total) * 100)}%`,
              //   style: {
              //     marginTop: '60px',
              //     right: '-55px',
              //   },
              //   closeButton: (
              //     <div
              //       style={{
              //         display: 'flex',
              //         alignItems: 'center',
              //         justifyContent: 'center',
              //       }}
              //       onClick={() => {
              //         toast.dismiss(toastId)
              //         return controller.abort()
              //       }}
              //     >
              //       <SVGCloseSmall />
              //     </div>
              //   ),
              // })
            }
          }

          // Combine all chunks into a single Blob
          const blob = new Blob(chunks, { type: 'video/mp4' })
          const url = window.URL.createObjectURL(blob)
          downloadFile(url, highestVideoQuality)

          // toast.update(toastId, {
          //   render: 'Download complete!',
          //   autoClose: 5000,
          //   closeButton: (
          //     <div
          //       style={{
          //         display: 'flex',
          //         alignItems: 'center',
          //         justifyContent: 'center',
          //       }}
          //       onClick={() => {
          //         toast.dismiss(toastId)
          //         return controller.abort()
          //       }}
          //     >
          //       <SVGCloseSmall />
          //     </div>
          //   ),
          // })
        } else {
          // Fallback to download without progress tracking
          const blob = await response.blob()
          const url = window.URL.createObjectURL(blob)
          downloadFile(url, highestVideoQuality)

          // toast.update(toastId, {
          //   render: 'Download complete!',
          //   autoClose: 5000,
          //   closeButton: (
          //     <div
          //       style={{
          //         display: 'flex',
          //         alignItems: 'center',
          //         justifyContent: 'center',
          //       }}
          //       onClick={() => {
          //         toast.dismiss(toastId)
          //         return controller.abort()
          //       }}
          //     >
          //       <SVGCloseSmall />
          //     </div>
          //   ),
          // })
        }
      } catch (error) {
        if (error.name === 'AbortError') {
          console.log('Fetch operation was aborted')
          toast.error('Download cancelled')
        } else {
          console.error('Fetch operation failed:', error)
        }
      }
    } else {
      toast.error('No suitable video quality found for download')
    }
  }

  const downloadFile = (url, videoQuality) => {
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = `${mediaFile?.filename}` // Use the name of the video quality object
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }

  const handleVideoDownload = async (e) => {
    e.stopPropagation()
    // Check if static_renditions is not set or does not have the ready status
    if (
      !sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.media_file?.static_renditions ||
      sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.media_file?.static_renditions
        .status !== 'ready'
    ) {
      // Make a request to the Mux API to check the status
      const file = getValues('media_file')

      /*  const result = await getMuxAssetsApi({
              upload_id: file?.mux_upload_id,
            })
            const asset = result.data.data[0]
       */
      // If the status of the static_renditions is ready
      if (file.static_renditions?.status === 'ready') {
        console.log('downloading video')
        await downloadVideo(file)
      } else {
        toast.error('This video is not ready for download yet, try again later')
      }
    } else {
      console.log('downloading video with media_file')

      await downloadVideo(sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.media_file)
    }
  }

  const setLessonValues = (lesson) => {
    if (lesson) {
      setValue('visibility', lesson.visibility)
      setValue('title', lesson.title)
      setValue('type', lesson.type)
      setValue('text', lesson.text)
      setValue('media_file', lesson.media_file)
      setValue('resource_files', lesson.resource_files || []) // Don't combine with newFiles here
      setValue('sequence', lesson.sequence)
      setValue('media_transcript', lesson.media_transcript)
      setValue('media_file_title', lesson.media_file_title)
      setValue('media_file_subtitle', lesson.media_file_subtitle)
      setValue('thumbnail_url', lesson.thumbnail_url)
    }
  }

  const selectLesson = (sectionId, lessonId) => {
    if (sections?.[sectionId]?.ContentLibraryCourseLesson?.[lessonId]) {
      reset()
      let lessonData = sections[sectionId].ContentLibraryCourseLesson[lessonId]
      setSelectedSectionId(sectionId)
      setSelectedLessonId(lessonId)
      setUploadStatus(null)
      setIsAudioLoaded(false)
      setIsVideoLoaded(false)
      setLessonValues(lessonData)
      setVideoDownloadReady(false)
      currentLessonIdRef.current = lessonId

      router.push(`${pathname}?lesson_id=${lessonId}`)
    }
  }

  const deleteLesson = async (sectionId, lessonId) => {
    if (isCreatingLesson.current) return
    // Optimistically update state
    const currentSequence = sections?.[sectionId]?.ContentLibraryCourseLesson?.[lessonId]?.sequence
    if (Object.keys(sections?.[sectionId]?.ContentLibraryCourseLesson).length > 1) {
      let sectionToRevert = null
      setSections((prevSections) => {
        const newSections = { ...prevSections }
        const section = newSections[sectionId]
        sectionToRevert = section
        delete section.ContentLibraryCourseLesson[lessonId]
        return newSections
      })

      try {
        await deleteLessonApi(library.id, courseId, sectionId, lessonId)
        const updatedSections = await fetchCourseData(library.id, courseId)
        /* search the previous to current sequence */
        const previousLessonThanDeleted =
          updatedSections[sectionId]?.ContentLibraryCourseLesson &&
          Object.values(updatedSections[sectionId]?.ContentLibraryCourseLesson).find(
            (lesson: any) => lesson.sequence === currentSequence - 1,
          )
        selectLesson(sectionId, (previousLessonThanDeleted as any).id)
        toast.success('Lesson deleted')
      } catch (error) {
        // If the API call fails, revert the change in the local state
        console.log('error', error)
        if (!sections?.[sectionId]?.ContentLibraryCourseLesson?.[lessonId]) {
          setSections((prevSections) => {
            const newSections = { ...prevSections }
            newSections[sectionId] = sectionToRevert
            return newSections
          })
        }
        toast.error('Failed to delete lesson')
      }
    }
  }

  const editSection = async (sectionId, newSectionData) => {
    // Update the local state first
    setSections((prevSections: any) => {
      const newSectionsObj = {
        ...prevSections,
        [sectionId]: {
          ...prevSections[sectionId],
          ...newSectionData,
        },
      }

      return newSectionsObj
    })

    try {
      await updateSectionApi(library.id, courseId, sectionId, newSectionData)
    } catch (error) {
      console.error('Failed to update section:', error)
    }
  }

  const handleAddResourcesClick = (event) => {
    setCurrentFile(null)
    setAddResourcesAnchor(event.currentTarget)
  }

  const saveLessonToApi = async (data) => {
    const res = await updateLessonApi(library.id, courseId, selectedSectionId, selectedLessonId, data)
    return res.data
  }

  const notAllowedToSave = (data) => {
    if (mediaType === 'text') return false
    console.log('mediaType', mediaType)
    const isVideoOrAudio =
      mediaType === 'video' || mediaType === 'audio' || data.type === 'video' || data.type === 'audio'

    return isVideoOrAudio && (!data.media_file || !mediaFile)
  }
  const saveCourseSettings = async (data) => {
    try {
      // Compare current form values with last saved values
      if (courseThumbnail) {
        data.thumbnail = {
          ...data.thumbnail,
          originalImg: courseThumbnail.originalImg,
          croppedImg: courseThumbnail.croppedImg,
        }
      }
      const res = await updateLibraryCourseApi(library.id, courseId as string, data)
      const courseData = res.data
      updateCourseSettings(courseData)
      setHasUnsavedChanges(false)

      // Update lastSavedSettings with the current form values
      setLastSavedSettings(data)
    } catch (error) {
      // Handle the error
      console.error(error)
    }
  }

  const onSubmit = async (data, showToasts = true) => {
    // Get the latest value of resource_files
    const isVideoOrAudio =
      mediaType === 'video' || mediaType === 'audio' || data.type === 'video' || data.type === 'audio'

    if (notAllowedToSave(data)) {
      toast.error(`Please upload ${mediaType || data.type} file or set media to none`)
      return
    }

    /* if the  course status is set to published but there is no published lesson, show toast Please publish a lesson to make your course available.*/
    const courseSettingsData = settingsGetValues()
    const publishedLessons: any = Object.values(sections).reduce((acc: any[], section: any) => {
      const lessons: any = Object.values(section.ContentLibraryCourseLesson)
      const publishedLessons = lessons.filter((lesson) => lesson.visibility === 'published')
      return [...acc, ...publishedLessons]
    }, [])
    if (
      courseSettingsData.visibility === 'published' &&
      publishedLessons.length === 0 &&
      getValues('visibility') === 'draft'
    ) {
      toast.error('Please publish a lesson to make your course available.')
      return
    }

    if (
      (courseSettingsData.visibility === 'published' && !courseSettingsData.title) ||
      courseSettingsData.title.trim() === ''
    ) {
      toast.error('Please add a title to your course.')
      return
    }

    const resourceFiles = getValues('resource_files')

    // Filter out the newly uploaded files from existing resource files
    const existingFiles = resourceFiles || []

    const uniqueFiles = Array.from(new Set(existingFiles.map((file) => JSON.stringify(file)))).map((file) =>
      existingFiles.find((f) => JSON.stringify(f) === file),
    )

    const sanitizedText = sanitizeQuillOutput(data.text)
    data.text = sanitizedText
    setValue('text', sanitizedText)

    const combinedData = {
      ...data,
      resource_files: uniqueFiles,
      media_file: isVideoOrAudio ? data.media_file : null,
    }

    try {
      await saveLessonToApi(combinedData)
      delete courseSettingsData.defaultValues
      delete courseSettingsData.access

      const areCourseSettingsEqual =
        courseSettingsData.title === lastSavedSettings.title &&
        courseSettingsData.description === lastSavedSettings.description &&
        courseSettingsData.visibility === lastSavedSettings.visibility &&
        courseSettingsData.thumbnail?.url === lastSavedSettings.thumbnail?.url
      const hasLessonVisibilityChanged =
        combinedData.visibility !== lastSavedLessonMap.current?.[selectedLessonId]?.visibility

      if (!areCourseSettingsEqual) {
        await saveCourseSettings(courseSettingsData)
      } else {
        console.log('skipping course settings saving')
      }
      if (hasLessonVisibilityChanged) {
        await fetchCourseData(library.id, courseId)
        lastSavedLessonMap.current[selectedLessonId] = combinedData
      } else {
        console.log('skipping lesson refetch')
      }
      setHasUnsavedChanges(false)

      const newSections = { ...sections }
      const section = newSections[selectedSectionId]
      section.ContentLibraryCourseLesson[selectedLessonId] = {
        ...section.ContentLibraryCourseLesson[selectedLessonId],
        ...combinedData,
      }
      setSections(newSections)

      if (showToasts) {
        toast.success('Course Updated')
      }
    } catch (error) {
      console.error(error)
      if (showToasts) {
        toast.error('Failed to update lesson. Please try again.')
      }
    }
  }
  const handleLinkDialogCancel = () => {
    setShowLinkDialog(false)
  }

  const handleResourceFileDialogCancel = () => {
    setCurrentFile(null)
    initUploadResourceFiles()
    setShowResourceFileDialog(false)
  }

  const handleDropVideoFile = (f) => {
    uploadInitiatorLessonIdRef.current = selectedLessonId
    setDisableLessonSwitch(true)
    initUploadFiles()
    setUploadStatus('uploading')
    handleUploadFiles([f], 'Mux', true, `library:${selectedLessonId}`)
  }

  const handleDropAudioFile = (f) => {
    uploadInitiatorLessonIdRef.current = selectedLessonId
    setDisableLessonSwitch(true)
    initUploadFiles()
    handleUploadFiles([f], 'Cloudinary')
  }

  const deleteFile = (file, confirmDeletion = true) => {
    // Update the newFiles state if the file is in newFiles
    resourceFileToBeDeletedRef.current = file

    if (confirmDeletion) {
      setShowConfirmDeleteResourceDialog(true)
      if (currentFile && currentFile.id === file.id) {
        setCurrentFile(null) // Reset currentFile if it was deleted
      }
    } else {
      initUploadResourceFiles()
      onConfirmDeleteResourceFile()
    }
  }

  const onConfirmDeleteResourceFile = async () => {
    const file = resourceFileToBeDeletedRef.current
    setValue(
      'resource_files',
      getValues('resource_files').filter((f) => f.public_id !== file.public_id),
    )
    if (file === currentFile) {
      setCurrentFile(null)
    }
    // Update the resource_files field value if the file is in resource_files
    const currentFiles = getValues('resource_files')
    const updatedFiles = currentFiles.filter((f) => f.public_id !== file.public_id)
    setValue('resource_files', updatedFiles)
    saveChanges(getValues())
    setResourceFilesCount(updatedFiles.length)
    setHasUnsavedChanges(true)
    hideCourseUpdatedToast.current = false
  }

  const saveChanges = async (data) => {
    // Save changes to the current lesson
    /* new lessons don't have an id so that's why we merge the id of the created lesson with the existing data */
    setHasUnsavedChanges(false)
    /* text field should be sanitized */
    const sanitizedText = sanitizeQuillOutput(data.text)
    data.text = sanitizedText
    const lessonData = await saveLessonToApi(data)
    const dataWithId = { ...lessonData, id: lessonData.id }

    setSections((prevSections) => {
      const newSections = { ...prevSections }
      const section = newSections[selectedSectionId]
      section.ContentLibraryCourseLesson[selectedLessonId] = {
        ...section.ContentLibraryCourseLesson[selectedLessonId],
        ...dataWithId,
      }
      return newSections
    })

    // Update the ID of the saved lesson
    /* savingLessonIdRef.current = selectedLessonId || lessonData.id */
  }

  // if there's a video still being uploaded or being processed, or if it's just a video that was loaded after refresh which will have the uploadStatus as null, don't allow the user to select another lesson
  const isVideoBeingUploaded = () => {
    return (
      mediaType === 'video' &&
      uploadStatus &&
      ((uploadProgress > 0 && uploadProgress < 100) || (uploadProgress === 100 && uploadStatus === 'processing'))
    )
  }

  const isAudioBeingUploaded = () => {
    return mediaType === 'audio' && uploadProgress > 0 && uploadProgress < 100 && !mediaFile
  }

  const disableAction = isVideoBeingUploaded() || isAudioBeingUploaded()

  const handleLessonClick = async (sectionId, lessonId) => {
    hasLessonBeenClickedRef.current = true
    if (disableLessonSwitch) {
      return
    }
    if (isVideoBeingUploaded() || isAudioBeingUploaded()) return

    if (notAllowedToSave(getValues())) {
      if (selectedLessonId !== lessonId) {
        toast.error(`Please upload ${mediaType || getValues().type} file.`)
        return
      }
    }
    if (selectedLessonId !== lessonId) {
      /*  setMediaType('text') */
      // Set savingLessonIdRef to the id of the current lesson
      if (hasUnsavedChanges) {
        savingLessonIdRef.current = selectedLessonId
        // Save changes to the current lesson in the background
        /* showToast('Saving changes...', 'info', { style: { marginTop: '60px', right: '-55px' } }) */

        const previousValues = getValues()
        selectLesson(sectionId, lessonId)
        setDisableLessonSwitch(true)

        saveChanges(previousValues)
          .then(() => {
            // Once the save operation is complete, clear savingLessonIdRef
            // this is because we added a turnaround for quill not to add extra line breaks when rerendering,
            // so currently we don't have a way to know if the content really change or if it's a reformat
            // we check if in conjunction with the text field, any other field has been edited, in that case we will display the toast
            const fieldApartFromQuillTextHasBeenEdited = Object.keys(formState.dirtyFields).some(
              (key) => key !== 'text',
            )
            const displayToast =
              hideCourseUpdatedToast.current === false ||
              (hideCourseUpdatedToast.current === true && fieldApartFromQuillTextHasBeenEdited)
            if (displayToast) {
              toast.success('Course Updated')
            }

            hideCourseUpdatedToast.current = false

            savingLessonIdRef.current = null

            router.push({
              pathname: pathname,
              query: { ...params, lesson_id: lessonId },
            })
            setTimeout(() => setDisableLessonSwitch(false), 1700)
            return
          })
          .catch((error) => {
            console.error('Failed to save changes:', error)
            // Handle any errors here, for example by showing a notification to the user
            savingLessonIdRef.current = null

            setDisableLessonSwitch(true)
            setTimeout(() => setDisableLessonSwitch(false), 1700)
          })
      } else {
        // Immediately select the new lesson
        selectLesson(sectionId, lessonId)
        setHasUnsavedChanges(false)

        setDisableLessonSwitch(true)
        setTimeout(() => setDisableLessonSwitch(false), 1300)
      }
    }
  }

  const isAppleDevice = typeof navigator !== 'undefined' && /Mac|iPad|iPhone|iPod/.test(navigator.platform)

  if (
    typeof window !== 'undefined' &&
    ((isAppleDevice && window.innerWidth <= 460) || (!isAppleDevice && window.innerWidth <= 600))
  ) {
    return (
      <Box
        sx={{
          backgroundColor: isDarkTheme ? '#17171a' : '#f3f5f5',
          height: '100%',
        }}
      >
        {/* <AppPageHeader onClickHamburger={() => null} /> */}
        <Box
          sx={{
            height: '100%',
            p: '0px 24px',
          }}
        >
          <DesktopOnly onGoBack={() => router.push('/library')} />
        </Box>
      </Box>
    )
  }

  const sanitizeQuillOutput = (html) => {
    if (!html) return
    let result = html.replace(/^((<p><br><\/p>){2,})/g, '')
    html.replace(/^((<h1><br><\/h1>){2,})/g, '')
    html.replace(/^((<h2><br><\/h2>){2,})/g, '')
    // Replace two or more consecutive <p><br></p> immediately before a <h1> to <h6> element with a single one
    result = result.replace(/((<p><br><\/p>){2,})(?=<h[1-6]>)/g, '<p><br></p>')
    return result
  }
  const section = sections?.[selectedSectionId]
  const sectionLessons = section?.ContentLibraryCourseLesson || []
  const publishedLessonsArray = Object.values(sectionLessons).filter((lesson: any) => lesson.visibility === 'published')
  const hasOnlyOnePublishedLesson = publishedLessonsArray.length === 1
  const disableLessonVisibilityDropdown =
    sectionLessons &&
    hasOnlyOnePublishedLesson &&
    section?.visibility === 'published' &&
    sectionLessons[selectedLessonId]?.visibility === 'published'
  // if the lesson is a draft and the text field and title is empty, we disable the visibility dropdown
  const isActiveLessonDraftBlank =
    getValues('visibility') === 'draft' &&
    ((!getValues('media_file') && getValues('type') !== 'text') ||
      (getValues('type') === 'text' && !getValues('text')) ||
      !getValues('title'))

  const handleSetSectionsFromMenu = (newSections) => {
    setSections(newSections)
    Object.entries(newSections).forEach((entry) => {
      const [_, sectionData] = entry
      const updatedLesson = Object.values(sectionData['ContentLibraryCourseLesson']).find(
        (l: any) => l.id === selectedLessonId,
      )
      if (updatedLesson) {
        setLessonValues(updatedLesson)
      }
    })
  }

  if (!library) {
    return null
  }

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit as any)} className="absolute left-0 top-0 z-10 h-full w-full">
        <Box className="w-100 h-100" sx={{ display: 'flex' }}>
          <Box
            sx={{
              height: '100%',
              width: hidenSidebar ? '0px' : '316px',
              minWidth: hidenSidebar ? '0px' : '316px',
              backgroundColor: isDarkTheme ? '#17171a' : '#f3f5f5',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              display: 'flex',
              flexDirection: 'column',
              transition: '0.5s cubic-bezier(.36,-0.01,0,.77)',
              position: 'absolute',
              zIndex: 100,
            }}
          >
            <CourseBuilderMenu
              sections={sections}
              setSections={handleSetSectionsFromMenu}
              isLoadingSections={isLoadingSections}
              addSection={addSection}
              addLesson={addLesson}
              selectLesson={selectLesson}
              deleteLesson={deleteLesson}
              editSection={editSection}
              deleteSection={deleteSection}
              selectedLessonId={selectedLessonId}
              courseSettings={courseSettings}
              onLessonClick={handleLessonClick}
              hasUnsavedChanges={hasUnsavedChanges}
              isCourseSettingsLoading={isCourseSettingsLoading}
              setHasUnsavedChanges={setHasUnsavedChanges}
              isCreatingLesson={isCreatingLesson.current}
              disableAction={disableAction || disableLessonSwitch}
              hasLessonBeenClicked={hasLessonBeenClickedRef.current}
              courseThumbnail={courseThumbnail}
              setCourseThumbnail={setCourseThumbnail}
              reset={settingsReset}
              setValue={settingsSetValue}
              descriptionValue={descriptionValue}
              titleValue={titleValue}
              control={settingsControl}
              hideCourseUpdatedToast={hideCourseUpdatedToast}
              getLessonValues={getValues}
              saveLesson={onSubmit}
              isDragging={isDragging}
              setIsDragging={setIsDragging}
              setSelectedSectionId={setSelectedSectionId}
            />
          </Box>
          <Box
            sx={{
              /*  width: hidenSidebar ? '100%' : 'calc(100% - 316px)', */
              transition: '0.5s cubic-bezier(.36,-0.01,0,.77)',
              backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
              overflowY: 'scroll',
              paddingLeft: hidenSidebar ? '0px' : '316px',
              width: '100%',
            }}
          >
            <Box
              sx={{
                backgroundColor: isDarkTheme ? '#28282b' : '#f3f5f5',
                height: '64px',
                padding: { xs: '0px 12px', sm: '0px 12px', md: '0px 12px', lg: '0px 24px' },
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                position: 'fixed',
                top: 0,
                zIndex: 999,
                width: hidenSidebar ? '100%' : 'calc(100% - 316px)',
              }}
            >
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                }}
              >
                <Typography
                  sx={{
                    fontFamily: 'Graphik Medium',
                    fontSize: '14px',
                    whiteSpace: 'nowrap',
                  }}
                >
                  Lesson Visibility
                </Typography>
                <Divider orientation="vertical" flexItem />
                <Controller
                  name="visibility"
                  control={control}
                  defaultValue="draft"
                  render={({ field: { value, onChange, onBlur }, fieldState: { error } }) => (
                    <FormControl error={Boolean(error)} className={clsx('form-control')} fullWidth>
                      <Select
                        variant="outlined"
                        disabled={isCreatingNew || disableLessonVisibilityDropdown || isActiveLessonDraftBlank}
                        error={Boolean(error)}
                        className={classes.inputFields}
                        value={value}
                        open={open}
                        onClose={() => setOpen(false)}
                        onOpen={() => setOpen(true)}
                        IconComponent={() => (
                          <div
                            style={{
                              cursor: isCreatingNew || disableLessonVisibilityDropdown ? 'cursor' : 'pointer',
                            }}
                            onClick={(e) => {
                              // disable action if the disable conditions are on
                              if (isCreatingNew || disableLessonVisibilityDropdown) return
                              setOpen(true)
                            }}
                          >
                            <SVGChevronDownLarge styles={{ color: 'rgb(141, 148, 163)' }} />
                          </div>
                        )}
                        onChange={(e) => {
                          onChange(e.target.value)
                          setHasUnsavedChanges(true)
                          hideCourseUpdatedToast.current = false
                        }}
                        onBlur={onBlur}
                        sx={{
                          backgroundColor: isDarkTheme ? '#313236' : '#ffffff',
                          zIndex: 1000,
                          height: '40px !important',
                          display: 'flex',
                          lineHeight: '14px',
                          padding: '0px 14px 0px 0px',
                          width: '146px',
                          alignItems: 'center',
                          borderRadius: '12px',
                          '& .MuiOutlinedInput-notchedOutline': {
                            border: 'none',
                          },
                          '& .MuiOutlinedInput-input': {
                            paddingRight: '0px !important',
                          },
                          '& .MuiListItem-root': {
                            lineHeight: '16px !important',
                          },
                        }}
                        data-cy="profile-personality-type-select-field"
                        MenuProps={{
                          PaperProps: {
                            sx: {
                              borderRadius: '12px',
                              boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                              border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                              backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                              backgroundImage: 'unset',
                              padding: '6px',
                              '& li.Mui-selected': {
                                backgroundColor: isDarkTheme ? '#1b1b1f !important' : '#f8f8f8  !important',
                                backgroundImage: 'unset',
                                borderRadius: '12px',
                              },
                              '& .MuiList-root': {
                                padding: '0px',
                              },
                            },
                          },
                        }}
                      >
                        {[
                          { value: 'draft', label: 'Draft' },
                          { value: 'published', label: 'Published' },
                        ].map((option) => (
                          <MenuItem
                            key={option.value}
                            value={option.value}
                            sx={{
                              '&:hover': {
                                backgroundColor: isDarkTheme ? '#1b1b1f' : '#e8e8e8',
                                borderRadius: '12px',
                              },
                            }}
                          >
                            <ListItem
                              sx={{
                                justifyContent: 'left',
                                paddingLeft: '5px',
                                paddingTop: '0px',
                                fontFamily: 'Graphik Medium',
                                fontSize: '14px',
                                color:
                                  option.value == 'published' && theme.palette.mode === 'dark'
                                    ? '#aee78b'
                                    : option.value == 'draft'
                                      ? '#e7ad8b'
                                      : '#48b705',
                                '& .MuiListItem-root': {
                                  lineHeight: '16px !important',
                                },
                                '&:hover': {
                                  backgroundColor: 'transparent',
                                },
                              }}
                            >
                              {option.value == 'draft' ? (
                                <Circle
                                  sx={{
                                    fontSize: '10px',
                                    color: 'rgb(231, 173, 139)',
                                    marginRight: '6px',
                                  }}
                                />
                              ) : (
                                <Circle
                                  sx={{
                                    fontSize: '10px',
                                    marginRight: '6px',
                                    color: theme.palette.mode === 'dark' ? '#AEE78B' : '#48b705',
                                  }}
                                />
                              )}
                              {option.label}
                            </ListItem>
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  )}
                />
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0px',
                }}
              >
                <AppTooltip
                  leaveDelay={200}
                  title={<Typography sx={{ fontFamily: 'Graphik Regular' }}>Preview Lesson</Typography>}
                  placement="top"
                >
                  <IconButton
                    className={classes.inputFields}
                    onClick={async () => {
                      if (hasUnsavedChanges) {
                        /*  await saveChanges(getValues()) */
                        toast.info('Autosaving...')
                        await onSubmit(getValues(), false)
                      }
                      router.push(`/${membership.slug}/content/course/${courseId}?lesson_id=${selectedLessonId}`)
                    }}
                    sx={{
                      backgroundColor: theme.palette.action.disabledBackground,
                      boxShadow: 'none',
                      borderRadius: '12px',
                      minWidth: '40px !important',
                      width: '40px !important',
                      marginLeft: '10px',
                    }}
                  >
                    <SVGEye styles={{ color: theme.palette.mode === 'dark' ? '#fff' : '#000' }} />
                  </IconButton>
                </AppTooltip>

                {autoSaving ? (
                  <div
                    style={{
                      backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
                      color: theme.palette.text.primary,
                      fontFamily: 'Graphik Medium',
                      fontSize: '14px',
                      marginRight: '8px',
                      marginLeft: '8px',
                      padding: '8px',
                      borderRadius: '12px',
                    }}
                  >
                    Auto Saving...
                  </div>
                ) : (
                  <LoadingButton
                    variant="contained"
                    className={classes.inputFields}
                    sx={{
                      backgroundColor: hasUnsavedChanges
                        ? adjustRGBA(theme.palette.primary.main, 0.8)
                        : theme.palette.action.disabledBackground,
                      boxShadow: 'none',
                      borderRadius: '12px',
                      color: hasUnsavedChanges ? '#fff' : theme.palette.text.primary,
                      fontFamily: 'Graphik Medium',
                      fontSize: '14px',
                      width: '102px',
                    }}
                    loading={formState.isSubmitting}
                    type="submit"
                    disabled={isCreatingNew || !formState.isValid || disableAction || disableLessonSwitch || isDragging}
                  >
                    Save
                  </LoadingButton>
                )}
              </Box>
            </Box>
            <Box
              sx={{
                maxWidth: 696,
                display: 'flex',
                flexDirection: 'column',
                mx: { xs: '50px', sm: '50px', md: 'auto' },
                padding: '80px 0px',
              }}
              className={classes.root}
            >
              <Box sx={{ mt: '40px' }}>
                <Typography
                  sx={{
                    color: theme.palette.text.disabled,
                    fontFamily: 'Graphik Semibold',
                    fontSize: '12px',
                    lineHeight: '16px',
                  }}
                >
                  {sections?.[selectedSectionId]?.name}
                </Typography>
              </Box>
              <Box>
                <Controller
                  name="title"
                  control={control}
                  defaultValue={''}
                  render={({ field }) => (
                    <InputBase
                      {...field}
                      sx={{
                        fontFamily: 'Graphik Semibold',
                        fontSize: '24px',
                        lineHeight: '32px',
                      }}
                      placeholder="New Lesson..."
                      fullWidth
                      inputProps={{ maxLength: 55 }}
                      onChange={(e) => {
                        field.onChange(e)
                        /* set local state with the new title, if there are some optimization problems in the future optimize this with a ref */
                        setSections((prevSections) => {
                          const newSections = { ...prevSections }
                          const section = newSections[selectedSectionId]
                          section.ContentLibraryCourseLesson[selectedLessonId] = {
                            ...section.ContentLibraryCourseLesson[selectedLessonId],
                            title: e.target.value,
                          }
                          return newSections
                        })

                        setHasUnsavedChanges(true)
                        hideCourseUpdatedToast.current = false
                      }}
                      disabled={isCreatingNew || savingLessonIdRef.current !== null}
                    />
                  )}
                />
              </Box>

              {!mediaFile && (
                <Box>
                  <Box
                    sx={{
                      borderRadius: '12px',
                      border: 'solid 1px',
                      borderColor: theme.palette.action.disabledBackground,
                      padding: '32px',
                    }}
                  >
                    <Typography
                      sx={{
                        fontFamily: 'Graphik Medium',
                        fontSize: '14px',
                        lineHeight: '16px',
                      }}
                    >
                      Media
                    </Typography>
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        marginTop: '16px',
                      }}
                    >
                      <Button
                        disabled={isCreatingNew || isVideoLoaded || isAudioLoaded}
                        startIcon={
                          <Box className={classes.mediaButtonIcon}>
                            <SVGVisibilityOff />
                          </Box>
                        }
                        onClick={() => {
                          if (
                            sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.type !==
                            'text'
                          ) {
                            setHasUnsavedChanges(true)
                            hideCourseUpdatedToast.current = false
                          }
                          setMediaType('text')
                          setValue('type', 'text')
                        }}
                        className={classes.mediaButtons}
                        style={{
                          color: mediaType === 'text' && theme.palette.text.primary,
                          border: mediaType === 'text' && `solid 1px ${theme.palette.text.primary}`,
                        }}
                      >
                        None
                      </Button>
                      <Button
                        className={classes.mediaButtons}
                        disabled={isCreatingNew || isVideoLoaded || isAudioLoaded}
                        startIcon={
                          <Box className={classes.mediaButtonIcon}>
                            <VideoCall fontSize="small" />
                          </Box>
                        }
                        onClick={() => {
                          if (
                            sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.type !==
                            'video'
                          ) {
                            setHasUnsavedChanges(true)
                            hideCourseUpdatedToast.current = false
                          }
                          setValue('type', 'video')
                          setMediaType('video')
                        }}
                        style={{
                          color: mediaType === 'video' && theme.palette.text.primary,
                          border: mediaType === 'video' && `solid 1px ${theme.palette.text.primary}`,
                        }}
                      >
                        Video
                      </Button>
                      <Button
                        className={classes.mediaButtons}
                        disabled={isCreatingNew || isVideoLoaded || isAudioLoaded}
                        startIcon={
                          <Box className={classes.mediaButtonIcon}>
                            <Mic fontSize="small" />
                          </Box>
                        }
                        onClick={() => {
                          if (
                            sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.type !==
                            'audio'
                          ) {
                            setHasUnsavedChanges(true)
                            hideCourseUpdatedToast.current = false
                          }
                          setValue('type', 'audio')
                          setMediaType('audio')
                        }}
                        style={{
                          color: mediaType === 'audio' && theme.palette.text.primary,
                          border: mediaType === 'audio' && `solid 1px ${theme.palette.text.primary}`,
                        }}
                      >
                        Audio
                      </Button>
                    </Box>
                    {mediaType === 'video' && (
                      <Box
                        className="background-color18 border-color02"
                        sx={{
                          mt: '16px',
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '12px',
                          borderStyle: 'dashed',
                          borderWidth: '1px',
                          height: '280px',
                        }}
                      >
                        <AppDropzone
                          width={550}
                          height={280}
                          file={mediaFile}
                          disabled={uploadStatus === 'uploading'}
                          accept={{
                            'video/*': ['.mp4', '.mov', '.avi'],
                          }}
                          onDropFile={handleDropVideoFile}
                          noLimit={true}
                          placeholder={
                            <Box
                              sx={{
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center',
                              }}
                            >
                              {isVideoLoaded && uploadProgress <= 100 && uploadStatus ? (
                                <Box
                                  sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                  }}
                                >
                                  {uploadStatus === 'uploading' ? (
                                    <Box sx={{ width: '100%' }}>
                                      <Typography
                                        sx={{
                                          color: isDarkTheme ? 'rgba(255,255,255,0.87)' : 'rgba(0,0,0,0.87)',
                                          fontSize: '14px',
                                          fontWeight: 500,
                                          mb: '6px',
                                          textAlign: 'center',
                                        }}
                                        className="font-family-graphik-medium"
                                      >
                                        Uploading...
                                        {uploadProgress > 100 ? 100 : Math.round(uploadProgress)}%
                                      </Typography>
                                      <LinearProgress
                                        color="inherit"
                                        variant="determinate"
                                        value={uploadProgress}
                                        sx={{
                                          width: '326px',
                                          mb: '6px',
                                          '.MuiLinearProgress-bar': {
                                            backgroundColor: `${theme.palette.primary.main} !important`,
                                          },
                                        }}
                                      />
                                      <Typography
                                        sx={{
                                          fontFamily: 'Graphik Regular',
                                          fontSize: '12px',
                                          fontWeight: 400,
                                          lineHeight: '16px',
                                          color: theme.palette.text.disabled,
                                          textAlign: 'center',
                                        }}
                                      >
                                        {capitalizeAllWords(uploadFiles?.[0]?.name || '')}
                                      </Typography>
                                    </Box>
                                  ) : null}

                                  {uploadStatus !== 'uploading' && uploadStatus ? (
                                    <>
                                      <CircularProgress
                                        sx={{
                                          width: '25px',
                                          height: '28px',
                                        }}
                                      />

                                      <Typography
                                        sx={{
                                          fontFamily: 'Graphik Regular',
                                          fontSize: '12px',
                                          fontWeight: 400,
                                          lineHeight: '16px',
                                          color: theme.palette.text.disabled,
                                          mt: '12px',
                                          mb: 3,
                                        }}
                                      >
                                        {uploadStatus === 'processing' ? 'Processing' : 'Generating transcription...'}
                                      </Typography>
                                      <Typography
                                        sx={{
                                          opacity: 1,
                                          color:
                                            theme.palette.mode === 'dark'
                                              ? 'rgba(255,255,255,0.87)'
                                              : 'rgba(0,0,0,0.87)',
                                          fontSize: '16px',
                                          fontWeight: 600,
                                          lineHeight: '20px',
                                        }}
                                      >
                                        {capitalizeAllWords(uploadFiles?.[0]?.name || '')
                                          .replace('.mp4.mp4', '.mp4')
                                          .replace('.mov.mov', '.mov')}
                                      </Typography>
                                    </>
                                  ) : null}
                                </Box>
                              ) : (
                                <>
                                  <Box
                                    sx={{
                                      backgroundColor: theme.palette.action.disabledBackground,
                                      width: '64px',
                                      height: '64px',
                                      borderRadius: '50%',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      color: theme.palette.text.disabled,
                                      cursor: 'pointer',
                                    }}
                                  >
                                    <SVGUpload width={24} height={24} />
                                  </Box>
                                  <Typography
                                    sx={{
                                      fontFamily: 'Graphik Semibold',
                                      fontSize: '16px',
                                      lineHeight: '20px',
                                      color: theme.palette.text.primary,
                                      mt: '16px',
                                    }}
                                  >
                                    Drag and drop video file to upload
                                  </Typography>
                                </>
                              )}
                              {isVideoLoaded && uploadProgress <= 100 ? (
                                <></>
                              ) : (
                                <>
                                  <Typography
                                    sx={{
                                      fontFamily: 'Graphik Regular',
                                      fontSize: '14px',
                                      lineHeight: '16px',
                                      color: theme.palette.text.disabled,
                                      mt: '12px',
                                      mb: '24px',
                                    }}
                                  >
                                    .mp4 or .mov
                                  </Typography>
                                  <Button
                                    sx={{
                                      borderRadius: '10px',
                                    }}
                                    color="primary"
                                    onClick={(e) => {}}
                                  >
                                    Select File
                                  </Button>
                                </>
                              )}
                            </Box>
                          }
                        />
                      </Box>
                    )}
                    {mediaType === 'audio' && (
                      <Box>
                        {mediaFile ? (
                          <Box
                            sx={{
                              position: 'relative',
                              width: '100%',
                              height: '50vh',
                            }}
                          >
                            <Box className={classes.audioPlayerWrapper}>
                              <AppAudioPlayer
                                showJumpControls={false}
                                /*                         thumbnailUrl={'/assets/default/images/icons/audio.png'}
                                 */ url={(mediaFile as any).url}
                              />
                            </Box>
                            <IconButton
                              sx={{ position: 'absolute', top: 0, right: 0 }}
                              onClick={() => {
                                setValue('media_file', null)
                                setHasUnsavedChanges(true)
                                hideCourseUpdatedToast.current = false
                              }}
                            >
                              <SVGCloseSmall />
                            </IconButton>
                          </Box>
                        ) : (
                          <Box
                            className="background-color18 border-color02"
                            sx={{
                              mt: '16px',
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderRadius: '12px',
                              borderStyle: 'dashed',
                              borderWidth: '1px',
                              height: '280px',
                            }}
                          >
                            <AppDropzone
                              width={550}
                              height={280}
                              file={mediaFile}
                              accept={AUDIO_ACCEPT_ONLY}
                              disabled={uploadStatus === 'uploading'}
                              onDropFile={handleDropAudioFile}
                              noLimit={true}
                              placeholder={
                                <Box
                                  sx={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    mt: isAudioLoaded ? undefined : '44px',
                                  }}
                                >
                                  {isAudioLoaded ? (
                                    <Box sx={{ width: '100%' }}>
                                      <Typography
                                        sx={{
                                          color: isDarkTheme ? 'rgba(255,255,255,0.87)' : 'rgba(0,0,0,0.87)',
                                          fontSize: '14px',
                                          fontWeight: 500,
                                          mb: '6px',
                                          textAlign: 'center',
                                        }}
                                        className="font-family-graphik-medium"
                                      >
                                        Uploading...
                                        {uploadProgress > 100 ? 100 : Math.round(uploadProgress)}%
                                      </Typography>
                                      <LinearProgress
                                        color="inherit"
                                        variant="determinate"
                                        value={uploadProgress}
                                        sx={{
                                          width: '326px',
                                          mb: '6px',
                                          '.MuiLinearProgress-bar': {
                                            backgroundColor: `${theme.palette.primary.main} !important`,
                                          },
                                        }}
                                      />
                                      <Typography
                                        sx={{
                                          fontFamily: 'Graphik Regular',
                                          fontSize: '12px',
                                          fontWeight: 400,
                                          lineHeight: '16px',
                                          color: theme.palette.text.disabled,
                                          textAlign: 'center',
                                        }}
                                      >
                                        {capitalizeAllWords(uploadFiles?.[0]?.name || '')}
                                      </Typography>
                                    </Box>
                                  ) : (
                                    <>
                                      <Box
                                        sx={{
                                          backgroundColor: theme.palette.action.disabledBackground,
                                          width: '64px',
                                          height: '64px',
                                          borderRadius: '50%',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center',
                                          color: theme.palette.text.disabled,
                                        }}
                                      >
                                        <SVGUpload width={24} height={24} />
                                      </Box>
                                      <Typography
                                        sx={{
                                          fontFamily: 'Graphik Semibold',
                                          fontSize: '16px',
                                          lineHeight: '20px',
                                          color: theme.palette.text.primary,
                                          mt: '16px',
                                        }}
                                      >
                                        Drag and drop audio file to upload
                                      </Typography>
                                      <Typography
                                        sx={{
                                          fontFamily: 'Graphik Regular',
                                          fontSize: '14px',
                                          lineHeight: '16px',
                                          color: theme.palette.text.disabled,
                                          mt: '12px',
                                        }}
                                      >
                                        .mp3, .wav, .m4a, .aac
                                      </Typography>
                                      <Button
                                        sx={{
                                          mt: '24px',
                                          mb: '48px',
                                          borderRadius: '10px',
                                        }}
                                        color="primary"
                                      >
                                        Select File
                                      </Button>
                                    </>
                                  )}
                                </Box>
                              }
                            />
                          </Box>
                        )}
                      </Box>
                    )}
                  </Box>
                </Box>
              )}

              {mediaFile && (
                <>
                  {mediaType === 'video' && (
                    <ShowVideoMedia
                      lessonFile={mediaFile}
                      uploadStatus={uploadStatus}
                      control={control}
                      selectedLessonId={selectedLessonId}
                      setValue={setValue}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                      setIsVideoLoaded={setIsVideoLoaded}
                      onVideoDownload={handleVideoDownload}
                      videoDownloadReady={videoDownloadReady}
                      setVideoDownloadReady={setVideoDownloadReady}
                      sections={sections}
                      setSections={setSections}
                      selectedSectionId={selectedSectionId}
                    />
                  )}
                  {mediaType === 'audio' && (
                    <ShowAudioMedia
                      lessonFile={mediaFile}
                      uploadStatus={uploadStatus}
                      control={control}
                      watch={watch}
                      selectedLessonId={selectedLessonId}
                      setValue={setValue}
                      setHasUnsavedChanges={setHasUnsavedChanges}
                      setIsAudioLoaded={setIsAudioLoaded}
                      sections={sections}
                      setSections={setSections}
                      selectedSectionId={selectedSectionId}
                    />
                  )}
                </>
              )}
              <Controller
                name="text"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <Box className="mt-6 min-h-32">
                    <Typography
                      sx={{
                        fontFamily: 'Graphik Semibold',
                        fontSize: '16px',
                        lineHeight: '20px',
                        mb: '4px',
                        color: theme.palette.text.primary,
                      }}
                    >
                      Description
                    </Typography>
                    <AppTextEditor
                      sx={{
                        fontFamily: 'Graphik Regular',
                        fontSize: '16px',
                        lineHeight: '28px',
                      }}
                      placeholder="Enter description"
                      modules={textEditorModules}
                      value={Boolean(field.value) ? field.value : '<p>Add description...</p>'}
                      onChange={(e: any) => {
                        field.onChange(e)
                        setHasUnsavedChanges(true)
                        /* we only want to hide the updated course toast if the only changed field is the quill editor that adds extra new lines and we reformat that, that looks as a fake unsaved change */
                        /* at this point it will have only 1 dirty field, 'text' which is this one*/
                        hideCourseUpdatedToast.current = true
                      }}
                      editorTheme="bubble"
                      disabled={isCreatingNew || savingLessonIdRef.current !== null}
                    />
                  </Box>
                )}
              />
              <Divider sx={{ mt: '32px' }} />
              <Box sx={{ mt: '32px' }}>
                <Box
                  sx={{
                    display: resourceFilesCount > 0 ? 'flex' : 'block',
                    mb: resourceFilesCount > 0 ? '16px' : '0px',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: 'Graphik Semibold',
                      fontSize: '16px',
                      lineHeight: '20px',
                      color: theme.palette.text.primary,
                    }}
                  >
                    Resources
                  </Typography>
                  {resourceFilesCount <= 0 && (
                    <Typography
                      sx={{
                        fontFamily: 'Graphik Regular',
                        fontSize: '12px',
                        lineHeight: '16px',
                        color: theme.palette.text.disabled,
                        mt: '8px',
                      }}
                    >
                      Add resources for members to view or download
                    </Typography>
                  )}
                  <LoadingButton
                    startIcon={<Add />}
                    onClick={handleAddResourcesClick}
                    sx={{
                      mt: resourceFilesCount > 0 ? '0px' : '16px',
                      height: '40px',
                      borderRadius: '10px !important',
                    }}
                    className={classes.buttons}
                  >
                    Add Resources
                  </LoadingButton>
                </Box>
                <StyledMenu
                  anchorEl={addResourcesAnchor}
                  open={Boolean(addResourcesAnchor)}
                  onClose={() => setAddResourcesAnchor(null)}
                >
                  <StyledListItem
                    onClick={() => {
                      setCurrentFile(null)
                      setAddResourcesAnchor(null)
                      setShowResourceFileDialog(true)
                    }}
                    sx={{ width: '155px' }}
                  >
                    Upload File
                  </StyledListItem>
                  <StyledListItem
                    sx={{ width: '155px' }}
                    onClick={() => {
                      setAddResourcesAnchor(null)
                      setCurrentLink(null)
                      setShowLinkDialog(true)
                    }}
                  >
                    Add Link
                  </StyledListItem>
                </StyledMenu>
                <AddLinkDialog
                  open={showLinkDialog}
                  text={currentLink?.text}
                  url={currentLink?.url}
                  public_id={currentLink?.public_id}
                  onCancel={handleLinkDialogCancel}
                  onSave={handleLinkDialogSave}
                />
                <ResourceFileDialog
                  open={showResourceFileDialog}
                  text={currentFile?.title}
                  url={currentFile?.url}
                  public_id={currentFile?.public_id}
                  onCancel={handleResourceFileDialogCancel}
                  onSave={handleResourceFileDialogSave}
                  handleFileSelect={handleFileSelect}
                  resourceUploadProgress={resourceUploadProgress}
                  currentFile={currentFile}
                  deleteFile={deleteFile}
                  initUploadResourceFiles={initUploadResourceFiles}
                  cancelResourceUploads={cancelResourceUploads}
                />
                <Controller
                  name="resource_files"
                  control={control}
                  defaultValue={[]}
                  render={({ field }) => (
                    <>
                      <Box sx={{ mt: 2 }}>
                        <FileAttachments
                          files={[...(field.value || [])]}
                          onDelete={deleteFile}
                          handleEditLink={handleEditLink}
                          handleEditResourceFile={handleEditResourceFile}
                          isModal={false}
                        />
                      </Box>
                    </>
                  )}
                />
                <AppWarning
                  title="Delete Resource File"
                  sx={{
                    width: '450px',
                    justifyContent: 'center',
                    margin: 'auto',
                    '& .proceed-button': {
                      backgroundColor: 'rgba(243, 70, 70, 0.8)',
                      color: '#FFFFFF',
                      '&:hover': {
                        backgroundColor: '#F34646',
                      },
                    },
                    '& .MuiButtonBase-root': {
                      borderRadius: '10px !important',
                      height: '48px',
                    },
                  }}
                  showTitle={false}
                  content={
                    <Box>
                      <SVGTrash width={20} height={24} styles={{ color: '#F34646', display: 'inline-block' }} />
                      <Typography
                        sx={{
                          fontFamily: 'Graphik Medium',
                          fontSize: '18px',
                          lineHeight: '24px',
                          marginTop: '18px',
                          marginBottom: '-5px',
                        }}
                      >
                        Permanently Delete?
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '14px',
                          fontFamily: 'Graphik Regular',
                          lineHeight: '20px',
                          color: theme.palette.text.disabled,
                          marginTop: '12px',
                        }}
                      >
                        You are about to delete a resource file.
                      </Typography>
                      <Typography
                        sx={{
                          fontSize: '14px',
                          fontFamily: 'Graphik Regular',
                          lineHeight: '20px',
                          color: theme.palette.text.disabled,
                          marginTop: '2px',
                          marginBottom: '-10px',
                        }}
                      >
                        This action cannot be undone.
                      </Typography>
                    </Box>
                  }
                  cancelButtonText="Cancel"
                  proceedButtonText="Delete"
                  open={showConfirmDeleteResourceDialog}
                  onClose={() => setShowConfirmDeleteResourceDialog(false)}
                  onProceed={() => {
                    onConfirmDeleteResourceFile()
                    setShowConfirmDeleteResourceDialog(false)
                  }}
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </form>
    </>
  )
}
