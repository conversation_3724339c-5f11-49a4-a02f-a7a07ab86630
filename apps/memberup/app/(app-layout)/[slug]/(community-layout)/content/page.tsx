'use client'

import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core'
import { arrayMove, rectSortingStrategy, SortableContext, sortableKeyboardCoordinates } from '@dnd-kit/sortable'
import { Add } from '@mui/icons-material'
import { Box, Button, Grid, useMediaQuery } from '@mui/material'
import Card from '@mui/material/Card'
import Typography from '@mui/material/Typography'
import * as Sentry from '@sentry/nextjs'
import { useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'

import {
  deleteCourseApi,
  getUserCoursesApi,
  updateContentLibraryApi,
} from '@memberup/shared/src/services/apis/content-library.api'
import { RECURRING_INTERVAL_ENUM } from '@memberup/shared/src/types/enum'
import { useStore } from '@/hooks/useStore'
import CourseCard from '@/memberup/components/cards/course-card'
import CreateCourse from '@/memberup/components/dialogs/library/create-course'
import AppWarning from '@/memberup/components/dialogs/warning'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import LibrarySkeleton from '@/memberup/components/library/library-loader'
import SVGTrash from '@/memberup/components/svgs/trash'
import { getContentLibrary, selectContentLibrary } from '@/memberup/store/features/contentLibrarySlice'
import { selectMembership, selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { useAppDispatch } from '@/memberup/store/store'

export default function ContentLibraryPage() {
  const dispatch = useAppDispatch()
  const router = useRouter()
  const [openCreateCourse, setOpenCreateCourse] = useState(false)
  const { contentLibrary } = useAppSelector((state) => selectContentLibrary(state))
  const [userCourses, setUserCourses] = useState([])
  const { isMobile, isDarkTheme, theme } = useAppTheme()

  const [anchorEl, setAnchorEl] = useState({})
  const [menuOpen, setMenuOpen] = useState(false)
  const [activeCard, setActiveCard] = useState(null)
  const [openWarning, setOpenWarning] = useState(false)
  const [courseToBeDeleted, setCourseToBeDeleted] = useState(null)
  const [isFetchingCourses, setIsFetchingCourses] = useState(true)
  const members = useSelector((state: any) => selectMembersMap(state))
  const isNewMobile = useMediaQuery('(max-width:600px)')
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const user = useStore((state) => state.auth.user)
  const currentUserProfile = user?.profile

  const { isCurrentUserAdmin } = useCheckUserRole()
  const loadedRef = useRef(false)

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleClick = (event, cardId) => {
    event.stopPropagation()
    setAnchorEl((prevState) => ({ ...prevState, [cardId]: event.currentTarget }))
    setMenuOpen(true)
    setActiveCard(cardId)
  }

  const handleClose = (event, cardId) => {
    event.stopPropagation()
    setAnchorEl((prevState) => ({ ...prevState, [cardId]: null }))
    setMenuOpen(false)
    setActiveCard(null)
  }

  const handleDeleteCourse = (event, courseId) => {
    event.stopPropagation()
    handleClose(event)
    setOpenWarning(true)
    setCourseToBeDeleted(courseId)
  }

  const onCardClick = (course) => {
    if (isCurrentUserAdmin && !isNewMobile) {
      router.push(`content/course-builder/${course.id}`)
    } else {
      router.push(`content/course/${course.id}`)
    }
  }

  const handleDragEnd = async (event) => {
    const { active, over } = event

    if (active.id !== over.id) {
      setUserCourses((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id)
        const newIndex = items.findIndex((item) => item.id === over.id)
        const result = arrayMove(items, oldIndex, newIndex)
        const orderedIds = result.map((item: any) => item.id)
        updateContentLibraryApi(contentLibrary.id, {
          id: contentLibrary.id,
          metadata: { course_order: orderedIds },
        })
        return result
      })
    }
  }

  async function fetchCourses() {
    try {
      const userCoursesResponse = await getUserCoursesApi(contentLibrary.id, membership.slug)
      let courses = userCoursesResponse.data.data.library_courses
      let course_order = courses?.[0]?.contentLibrary?.metadata?.course_order
      if (course_order) {
        const uniqueCourseOrder = [...new Set(course_order)]
        courses = uniqueCourseOrder
          .map((id) => courses.find((course) => course.id === id))
          .filter((course) => course !== undefined)
      }

      setUserCourses(courses)
    } catch (err) {
      console.log(err)
      Sentry.captureException(err)
    } finally {
      setIsFetchingCourses(false)
    }
  }

  useEffect(() => {
    dispatch(getContentLibrary(membership.id))
  }, [])

  useEffect(() => {
    if (contentLibrary?.id && !loadedRef.current) {
      loadedRef.current = true
      fetchCourses()
    }
  }, [contentLibrary])

  const canCreateCourse =
    isCurrentUserAdmin &&
    (membershipSetting.plan === 'enterprise' ||
      (membershipSetting.plan === 'pro' && userCourses.length < 3) ||
      (membershipSetting.plan === 'basic' && userCourses.length < 1))

  return (
    <>
      <Box
        sx={{
          height: '100vh',
        }}
      >
        <Box sx={{ p: 4 }}>
          <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
            <SortableContext items={userCourses} strategy={rectSortingStrategy}>
              <Grid container spacing={4} style={isFetchingCourses ? { display: 'flex' } : {}}>
                {isFetchingCourses || (!userCourses.length && !isCurrentUserAdmin) ? (
                  <Grid item xs={12} sm={6} md={6} lg={4}>
                    <LibrarySkeleton width={'100%'} />
                  </Grid>
                ) : (
                  userCourses.map((course) => (
                    <Grid item xs={12} sm={6} md={6} lg={4} key={course.id}>
                      <CourseCard
                        course={course}
                        menuOpen={menuOpen}
                        activeCard={activeCard}
                        handleClick={handleClick}
                        anchorEl={anchorEl[course.id]}
                        handleClose={handleClose}
                        handleDeleteCourse={handleDeleteCourse}
                        currentUserProfile={currentUserProfile}
                        theme={theme}
                        onCardClick={() => onCardClick(course)}
                        membersCount={Object.keys(members).length || 0}
                        id={course.id}
                      />
                    </Grid>
                  ))
                )}

                {isCurrentUserAdmin && !isMobile ? (
                  <Grid item xs={12} sm={6} md={4} style={{ display: 'flex', alignItems: 'stretch' }}>
                    <Card
                      sx={{
                        flex: 1,
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        border: '1px dashed',
                        backgroundColor: 'rgba(141, 148, 163, 0.04)',
                        borderRadius: '12px',
                        height: '375px',
                        display: 'flex',
                        cursor: 'pointer',
                      }}
                      onClick={() => {
                        setOpenCreateCourse(true)
                      }}
                    >
                      <Button
                        sx={{
                          borderColor: isDarkTheme ? 'rgba(141, 148, 163, 0.12)' : theme.palette.primary.main,
                          borderRadius: '10px',
                          padding: '0px 12px',
                        }}
                        variant="outlined"
                        startIcon={
                          <Add
                            sx={{
                              color: isDarkTheme ? 'rgb(141, 148, 163)' : theme.palette.primary.main,
                              marginRight: '-4px',
                            }}
                          />
                        }
                      >
                        <Typography
                          sx={{
                            fontFamily: 'Graphik Semibold',
                            fontSize: '14px',
                            fontWeight: '500',
                            color: isDarkTheme ? 'rgb(141, 148, 163)' : theme.palette.primary.main,
                          }}
                        >
                          Add Course
                        </Typography>
                      </Button>
                    </Card>
                  </Grid>
                ) : null}
              </Grid>
            </SortableContext>
          </DndContext>
        </Box>

        {openCreateCourse ? (
          <CreateCourse
            open={openCreateCourse}
            onClose={() => {
              setOpenCreateCourse(false)
            }}
            contentLibrary={contentLibrary}
          />
        ) : null}
        <AppWarning
          sx={{
            '& .proceed-button': {
              backgroundColor: '#F34646',
              color: '#FFFFFF',
              '&:hover': {
                backgroundColor: '#F34646',
              },
            },
            '& .MuiButton-root': {
              borderRadius: '10px !important',
            },
          }}
          showTitle={false}
          content={
            <Box>
              <SVGTrash width={20} height={24} styles={{ color: '#F34646', display: 'inline-block' }} />
              <Typography
                sx={{
                  fontFamily: 'Graphik Medium',
                  fontSize: '18px',
                  lineHeight: '24px',
                  marginTop: '18px',
                }}
              >
                Permanently Delete?
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '12px',
                }}
              >
                Deleting the selected course will also delete all the sections and content associated with this course.
              </Typography>
              <Typography
                sx={{
                  fontSize: '14px',
                  fontFamily: 'Graphik Regular',
                  lineHeight: '20px',
                  color: theme.palette.text.disabled,
                  marginTop: '20px',
                }}
              >
                This action cannot be undone.
              </Typography>
            </Box>
          }
          cancelButtonText="Cancel"
          proceedButtonText="Confirm"
          open={openWarning}
          onProceed={async () => {
            try {
              await deleteCourseApi(contentLibrary.id, courseToBeDeleted)
              setOpenWarning(false)
            } catch (err) {
              console.log(err)
            } finally {
              setCourseToBeDeleted(null)
            }
          }}
          onClose={() => setOpenWarning(false)}
        />
      </Box>
    </>
  )
}
