'use client'

import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import useTheme from '@mui/material/styles/useTheme'
import useMediaQuery from '@mui/material/useMediaQuery'
import { useEffect, useState } from 'react'
import { MessageInput, Channel as ReactStreamChatChannel, useChatContext } from 'stream-chat-react'

import { useBrowserLayoutEffect } from '@memberup/shared/src/components/hooks/use-browser-layout-effect'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { FixedHeightLayout } from '@/components/layout/fixed-height-layout'
import { SkeletonBox } from '@/components/ui'
import AppMessagingChannelHeader from '@/memberup/components/inbox/messaging-channel-header'
import AppMessagingChannelList from '@/memberup/components/inbox/messaging-channel-list'
import { AppMessagingContextProvider, useAppMessagingContext } from '@/memberup/components/inbox/messaging-context'
import AppMessagingCreateChannel from '@/memberup/components/inbox/messaging-create-channel'
import AppMessagingEditMessage from '@/memberup/components/inbox/messaging-edit-message'
import AppMessagingEmptyPlaceholder from '@/memberup/components/inbox/messaging-empty-placeholder'
import AppMessagingMessageList from '@/memberup/components/inbox/messaging-message-list'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

function Inbox() {
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const isSmDown = useMediaQuery(theme.breakpoints.down('sm'))
  const user = useAppSelector((state) => selectUser(state))
  const { client: streamChatClient, channel: streamChatChannel, setActiveChannel } = useChatContext()
  const { openCreateMessagingChannel, setOpenCreateMessagingChannel } = useAppMessagingContext()
  const [_, setRefreshInboxSection] = useState(0)
  const [isUserInChannel, setIsUserInChannel] = useState(false)

  useBrowserLayoutEffect(() => {
    setActiveChannel?.(null)
  }, [setActiveChannel])

  useEffect(() => {
    let streamChatChannelEventListener
    if (mountedRef.current) {
      if (streamChatChannel) {
        setOpenCreateMessagingChannel(false)
      }

      // Check if the user is a member of the current active channel
      if (streamChatChannel?.state?.members[user?.id]) {
        setIsUserInChannel(true)
      } else {
        setIsUserInChannel(false)
      }

      streamChatChannelEventListener = streamChatChannel?.on((e) => {
        switch (e.type) {
          case 'member.removed':
            if (e.user.id === user?.id) {
              setRefreshInboxSection((prevState) => prevState + 1)
              setIsUserInChannel(false)
            }
            break
        }
      })
    }

    return () => {
      streamChatChannelEventListener?.unsubscribe?.()
    }
  }, [streamChatChannel, user?.id])

  if (!user?.id || !streamChatClient) return null

  return (
    <FixedHeightLayout>
      <Box
        className="inbox-container grow"
        sx={{
          color: theme.palette.text.disabled,
          height: '100%',
          position: 'relative',
          overflow: 'hidden',
          '& .str-chat.messaging': {
            backgroundColor: 'transparent',
            width: '100%',
            height: '100%',
            minHeight: '100%',
            position: 'relative',
            left: 0,
            top: 0,
          },
          '& .str-chat__channel-list-empty-v1': {
            display: 'none',
          },
        }}
      >
        <Grid
          className="inbox-grid page-inner-pb"
          container
          columnSpacing={isSmDown ? 0 : 4}
          sx={{ height: { xs: '100%', sm: '100%', marginLeft: 0, marginTop: 0, width: '100%' } }}
        >
          <Grid
            className="channel-list-container pl-0"
            item
            xs={12}
            sm={4}
            sx={{
              height: '100%',
              display: {
                xs: openCreateMessagingChannel || streamChatChannel ? 'none' : 'block',
                sm: 'block',
              },
            }}
          >
            <AppMessagingChannelList onCreateChannel={() => setOpenCreateMessagingChannel(true)} />
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            sx={{
              height: '100%',
              display: {
                xs: openCreateMessagingChannel || streamChatChannel ? 'block' : 'none',
                sm: 'block',
              },
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                borderRadius: 'var(--container-border-radius)',
                height: '100%',
                overflow: 'hidden',
                boxShadow: '0px 1px 2px rgba(0,0,0, 0.01)',
                '& .str-chat__container': {
                  height: '100%',
                },
                '& .custom-channel-class': {
                  height: '100%',
                },
              }}
            >
              {openCreateMessagingChannel ? (
                <>
                  <AppMessagingCreateChannel onClose={() => setOpenCreateMessagingChannel(false)} />
                  <Box className="bg-white-500 dark:bg-grey-900" sx={{ flex: 1 }}></Box>
                </>
              ) : (
                <ReactStreamChatChannel
                  channel={streamChatChannel}
                  maxNumberOfFiles={10}
                  multipleUploads={true}
                  EmptyPlaceholder={<AppMessagingEmptyPlaceholder />}
                >
                  {isUserInChannel && (
                    <Box
                      sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        width: '100%',
                        height: '100%',
                      }}
                    >
                      <AppMessagingChannelHeader
                        onClose={() => {
                          setOpenCreateMessagingChannel(false)
                          setActiveChannel(null)
                        }}
                      />
                      <Box className="bg-white-500 dark:bg-grey-900" sx={{ flex: 1, overflowY: 'auto' }}>
                        <AppMessagingMessageList />
                      </Box>
                      <Box className="bg-white-500 dark:bg-grey-900" sx={{ minHeight: 52 }}>
                        <MessageInput focus Input={AppMessagingEditMessage} />
                      </Box>
                    </Box>
                  )}
                </ReactStreamChatChannel>
              )}
            </Box>
          </Grid>
        </Grid>
      </Box>
    </FixedHeightLayout>
  )
}

export default function InboxPage() {
  const { client: streamChatClient } = useChatContext()
  if (!streamChatClient?.user) {
    return (
      <FixedHeightLayout>
        <SkeletonBox />
      </FixedHeightLayout>
    )
  }

  return (
    <AppMessagingContextProvider>
      <Inbox />
    </AppMessagingContextProvider>
  )
}
