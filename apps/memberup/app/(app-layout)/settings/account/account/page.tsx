'use client'

import AccountSettings from '@/components/settings/account-settings/AccountSettings'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

export default function AccountSettingsPage() {
  const user = useAppSelector((state) => selectUser(state))

  if (!user) return 'Loading account settings'

  return <AccountSettings />
}
