import { useEffect, useState } from 'react'

import { Button, Input } from '@/components/ui'
import { getRewardfulAffiliateApi } from '@/shared-services/apis/rewardful.api'

export default function ReferralsPage() {
  const [referralLink, setReferralLink] = useState(null)

  const getReferralLink = async () => {
    const response = await getRewardfulAffiliateApi()

    if (response.data?.success) {
      setReferralLink(response.data?.data?.affiliateUrl)
    }
  }

  useEffect(() => {
    getReferralLink()
  }, [])

  return (
    <div className="tailwind-component page-inner-pb">
      <div className="rounded-box max-w-screen-md p-5">
        <h2 className="-mt-1 mb-2 text-lg font-semibold">Referrals</h2>
        <p className="mb-5 text-sm text-black-100">
          Refer people to MemberUp and earn 20% of monthly recurring revenue for life!{' '}
          <a href="https://memberup.com/affiliates" className="text-primary" target="_blank">
            Learn More
          </a>
        </p>
        <Input
          placeholder="Referral Link"
          rightContent={
            <Button
              size="sm"
              variant="secondary"
              className="hover-bg-black-100 dark:hover-bg-black-500 bg-black-200 dark:bg-black-700"
              onClick={() => {}}
            >
              Copy
            </Button>
          }
          value={referralLink}
        />
      </div>
    </div>
  )
}
