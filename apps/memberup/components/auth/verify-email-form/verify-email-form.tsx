import { REGEXP_ONLY_DIGITS } from 'input-otp'
import { useState } from 'react'

import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { Button } from '@/components/ui'
import { InputOTP, InputOTPGroup, InputOTPSlot } from '@/components/ui/input-otp'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { resendEmailVerificationCodeApi, verifyEmailApi } from '@/shared-services/apis/user.api'

export const VerifyEmailForm = () => {
  const user = useStore((state) => state.auth.user)
  const updateUser = useStore((state) => state.auth.updateUser)
  const setKnockToken = useStore((state) => state.auth.setKnockToken)
  const setStreamChatUserToken = useStore((state) => state.auth.setStreamChatUserToken)
  const [loading, setLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState(null)
  const [requestVerifyEmail, setRequestVerifyEmail] = useState(false)
  const [OTPValue, setOTPValue] = useState('')
  const handleVerifyEmailButtonClick = async () => {
    setRequestVerifyEmail(true)

    try {
      const { data } = await verifyEmailApi(OTPValue)

      setKnockToken(data.knockToken)
      setStreamChatUserToken(data.streamChatUserToken)
      updateUser({
        status: USER_STATUS_ENUM.active,
      })
    } catch (err) {
      if (err.response?.request?.status === 400) {
        setErrorMessage('Verification code not valid. Please try again.')
      } else {
        setErrorMessage(formSubmitError)
      }
      setRequestVerifyEmail(false)
    }
  }

  const handleResend = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault()
    setLoading(true)
    await resendEmailVerificationCodeApi()
    setLoading(false)
    toast.info(`We have sent your email verification code to ${user.email}`)
  }

  const onOTPChange = (value: string) => {
    setOTPValue(value)
  }
  const OTPValid = OTPValue.length === 6

  return (
    <div className="w-full flex-col items-start justify-start gap-6">
      <div className="mb-5 flex flex-col items-center justify-start gap-1.5">
        <div className="text-white text-center font-['Graphik'] text-lg font-semibold leading-normal">
          We sent you a code
        </div>
        <div className="text-center font-['Graphik'] text-base font-normal leading-normal text-gray-400">
          {`Verify ${user.email}`}
        </div>
      </div>
      <div className="flex w-full flex-col items-center">
        <InputOTP maxLength={6} onChange={onOTPChange} pattern={REGEXP_ONLY_DIGITS} value={OTPValue}>
          <InputOTPGroup>
            <InputOTPSlot index={0} />
            <InputOTPSlot index={1} />
            <InputOTPSlot index={2} />
            <InputOTPSlot index={3} />
            <InputOTPSlot index={4} />
            <InputOTPSlot index={5} />
          </InputOTPGroup>
        </InputOTP>
        {errorMessage && <div className="mt-3 text-sm text-red-200">{errorMessage}</div>}

        <Button
          className="mt-5 w-full"
          type="submit"
          variant="default"
          loading={requestVerifyEmail}
          disabled={!OTPValid || requestVerifyEmail}
          onClick={handleVerifyEmailButtonClick}
          data-cy="verify-email-button"
        >
          Next
        </Button>
      </div>
      {!loading && (
        <div className="w-full pt-4 text-center text-ssm text-gray-400">
          Didn't get the email?{' '}
          <Button className="text-ssm text-violet-600 underline" onClick={(e) => handleResend(e)} variant="inline">
            Resend it
          </Button>
        </div>
      )}
    </div>
  )
}
