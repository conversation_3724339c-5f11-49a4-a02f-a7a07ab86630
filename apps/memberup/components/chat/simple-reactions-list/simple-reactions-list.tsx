// Component based on
// https://github.com/GetStream/stream-chat-react/blob/e81fc6955def2f5d240e2095f4bf2325f07c5f78/src/components/Reactions/SimpleReactionsList.tsx
import clsx from 'clsx'
import React from 'react'
import type { ReactionGroupResponse, ReactionResponse } from 'stream-chat'
import { MessageContextValue, useMessageContext } from 'stream-chat-react'
import type { DefaultStreamChatGenerics, ReactionOptions } from 'stream-chat-react'

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { useProcessReactions } from '@/hooks/chat/useProcessReactions'

export type SimpleReactionsListProps<StreamChatGenerics extends DefaultStreamChatGenerics = DefaultStreamChatGenerics> =
  Partial<Pick<MessageContextValue, 'handleFetchReactions' | 'handleReaction'>> & {
    /** An array of the own reaction objects to distinguish own reactions visually */
    own_reactions?: ReactionResponse<StreamChatGenerics>[]
    /**
     * An object that keeps track of the count of each type of reaction on a message
     * @deprecated This override value is no longer taken into account. Use `reaction_groups` to override reaction counts instead.
     * */
    reaction_counts?: Record<string, number>
    /** An object containing summary for each reaction type on a message */
    reaction_groups?: Record<string, ReactionGroupResponse>
    /** A list of the currently supported reactions on a message */
    reactionOptions?: ReactionOptions
    /** An array of the reaction objects to display in the list */
    reactions?: ReactionResponse<StreamChatGenerics>[]
  }

const UnMemoizedSimpleReactionsList = <
  StreamChatGenerics extends DefaultStreamChatGenerics = DefaultStreamChatGenerics,
>(
  props: SimpleReactionsListProps<StreamChatGenerics>,
) => {
  const { handleReaction: propHandleReaction, ...rest } = props

  const { handleReaction: contextHandleReaction } = useMessageContext<StreamChatGenerics>('SimpleReactionsList')

  const { existingReactions, hasReactions, totalReactionCount } = useProcessReactions(rest)

  const handleReaction = propHandleReaction || contextHandleReaction

  if (!hasReactions) return null

  return (
    <div className="str-chat__message-reactions-container">
      <ul
        className="str-chat__simple-reactions-list str-chat__message-reactions flex space-x-1"
        data-testid="simple-reaction-list"
      >
        {existingReactions.map(({ EmojiComponent, isOwnReaction, latestReactedUserNames, reactionType }) => {
          const tooltipContent = latestReactedUserNames.join(', ')

          return (
            EmojiComponent && (
              <li
                className={clsx('str-chat__simple-reactions-list-item', {
                  'str-chat__message-reaction-own': isOwnReaction,
                })}
                key={reactionType}
                onClick={(event) => handleReaction(reactionType, event)}
                onKeyUp={(event) => handleReaction(reactionType, event)}
              >
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex">
                        <EmojiComponent />
                        {latestReactedUserNames.length > 1 && (
                          <span className="ml-0.5 text-ssm font-semibold">{latestReactedUserNames.length}</span>
                        )}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>{tooltipContent}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </li>
            )
          )
        })}
        {<li className="str-chat__simple-reactions-list-item--last-number">{totalReactionCount}</li>}
      </ul>
    </div>
  )
}

export const SimpleReactionsList = React.memo(UnMemoizedSimpleReactionsList) as typeof UnMemoizedSimpleReactionsList
