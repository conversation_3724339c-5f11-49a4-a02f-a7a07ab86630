import { Comment } from './comment'
import { cn } from '@/lib/utils'

export function CommentThread({ className, comment }: { className?: string; comment: any }) {
  return (
    <div className={cn('comment-thread tailwind-component', className)}>
      <Comment commentData={comment} members={{}} />
      {comment.replies && comment.replies.length > 0 && (
        <div className="ml-[3.25rem] flex flex-col space-y-4 pt-4">
          {comment.replies.map((reply: any) => (
            <Comment key={reply.id} commentData={reply} members={{}} />
          ))}
        </div>
      )}
    </div>
  )
}
