'use client'

import Link from 'next/link'
import { useCallback, useState } from 'react'
import { useChatContext } from 'stream-chat-react'

import EditComment from './edit-comment'
import { formatDateLong } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import NewComment from '@/components/feed/new-comment'
import { Verified16Icon } from '@/components/icons/16px/verified-16-icon'
import { MoreHorizontal20Icon } from '@/components/icons/20px/MoreHorizontal20Icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button } from '@/components/ui'
import {
  AdaptiveTooltip,
  AdaptiveTooltipContent,
  Adaptive<PERSON>ooltipProvider,
  AdaptiveTooltipTrigger,
} from '@/components/ui/adaptive-tooltip'
import { ConfirmModal } from '@/components/ui/confirm-modal'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import { toast } from '@/components/ui/sonner'
import useFeedLike from '@/hooks/feed/use-feed-like'
import { useStore } from '@/hooks/useStore'
import { formatDistanceToNowMinimal } from '@/lib/dates'
import { formSubmitError } from '@/lib/error-messages'
import { cn } from '@/lib/utils'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { deleteFeedApi, reportFeedApi } from '@/shared-services/apis/feed.api'
import { IReaction } from '@/shared-types/interfaces'
import checkStreamUserRole from '@/src/components/hooks/check-stream-user-role'
import useCheckUserRole from '@/src/components/hooks/use-check-user-role'
import { useRenderTextWithMentions } from '@/src/components/hooks/use-render-with-mentions'
import SVGHeart from '@/src/components/svgs/heart'
import SVGHeartFilled from '@/src/components/svgs/heart-filled'

const LIKE_BUTTON_COOLDOWN_MS = 500

export function Comment({
  className,
  commentData,
  members,
  parentMessage,
  replies = [],
  onShowAllReplies,
  editingCommentIds,
  addEditingCommentId,
  removeEditingCommentId,
}: {
  className?: string
  commentData: any
  members: { [key: string]: IUser }
  parentMessage?: IFeed
  replies?: any
  onShowAllReplies?: () => void
  editingCommentIds: string[]
  addEditingCommentId: (id: string) => void
  removeEditingCommentId: (id: string) => void
}) {
  const user = useStore((state) => state.auth.user)
  const membership = useStore((state) => state.community.membership)
  const { isCurrentUserAdmin } = useCheckUserRole()
  const { client: streamChatClient } = useChatContext()
  const { handleLike } = useFeedLike()
  const initialLikedState = commentData.own_reactions?.some((r: IReaction) => r.user_id === user?.id)
  const [liked, setLiked] = useState(initialLikedState)

  const [isLikeButtonDisabled, setIsLikeButtonDisabled] = useState(false)
  const [visibleNewReply, setVisibleNewReply] = useState<boolean>(false)
  const [showAllReplies, setShowAllReplies] = useState(false)

  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isReporting, setIsReporting] = useState(false)
  const [openEditComment, setOpenEditComment] = useState(false)
  const [actionsDropdownOpen, setActionsDropdownOpen] = useState(false)

  const MAX_REPLIES_TO_SHOW_BY_DEFAULT = 2

  let repliesToRender = []

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)
  const { isAdminOrCreatorActor } = checkStreamUserRole(commentData.user, members)

  // Permission checks
  const isCommentOwner = commentData.user.id === user?.id
  const canDeleteComment = isCommentOwner || isCurrentUserAdmin
  const canReportComment = !isCommentOwner // Users can't report their own comments
  const canEditComment = isCommentOwner && isUserAllowedToPost

  const renderedText = useRenderTextWithMentions(
    commentData?.text,
    commentData?.mentioned_users,
    members || {},
    'comment',
    commentData as IFeed,
  )

  const createdTimeAgo = formatDistanceToNowMinimal(new Date(commentData?.created_at))
  const createdAtLong = formatDateLong(new Date(commentData?.created_at))

  const otherReplies = []
  const repliesFromReactions = [] //reactions.filter((item) => item.type.indexOf('reply') >= 0)
  const allReplies = [...repliesFromReactions, ...otherReplies, ...replies]

  const repliesToShowByDefault = Math.min(allReplies.length, MAX_REPLIES_TO_SHOW_BY_DEFAULT)
  repliesToRender = !showAllReplies ? allReplies.slice(0, repliesToShowByDefault) : allReplies
  const collapsedReplies = !showAllReplies ? allReplies.slice(repliesToShowByDefault) : []
  const remainingRepliesToShow = collapsedReplies.length

  const handleLikeClick = () => {
    handleLike(commentData, liked ? 'unliked' : 'liked')
    setIsLikeButtonDisabled(true)
    setLiked((state) => !state)
    setTimeout(() => {
      setIsLikeButtonDisabled(false)
    }, LIKE_BUTTON_COOLDOWN_MS)
  }

  const handleReplyClick = () => {
    setVisibleNewReply(true)
  }

  const handleViewMoreReplies = () => {
    setShowAllReplies(true)
    onShowAllReplies?.()
  }

  const handleCancel = useCallback(() => setVisibleNewReply(false), [])

  const handleDeleteComment = async () => {
    setIsDeleting(true)
    try {
      if (streamChatClient && commentData.id) {
        await streamChatClient.deleteMessage(commentData.id, true) // hard delete
        await deleteFeedApi(commentData.id)
        toast.success('Comment deleted successfully')
      }
    } catch {
      toast.error(formSubmitError)
    } finally {
      setIsDeleting(false)
      setShowDeleteConfirmation(false)
    }
  }

  const handleReportComment = async () => {
    setIsReporting(true)
    try {
      const payload = {
        feed_status: FEED_STATUS_ENUM.reported,
        reports: [
          ...((commentData as IFeed).reports || []),
          {
            date: new Date().toUTCString(),
            name: getFullName(user.first_name, user.last_name, ''),
            email: user.email,
          },
        ],
      }

      await reportFeedApi(commentData.id, payload)
      toast.success('Successfully reported to community admins.')
    } catch {
      toast.error(formSubmitError)
    } finally {
      setIsReporting(false)
    }
  }

  const likeCount = () => {
    const baseCount = commentData.reaction_counts?.like || 0
    if (liked && !initialLikedState) return baseCount + 1
    if (!liked && initialLikedState) return baseCount - 1
    return baseCount
  }

  const shouldShowDropdown = (canDeleteComment || canReportComment) && isUserAllowedToPost

  return (
    <div className={cn('flex gap-1.5', className)}>
      <ProfilePicture
        className="h-10 w-10 shrink-0"
        src={commentData?.user?.image || ''}
        cropArea={commentData?.user?.image_crop_area}
        alt={commentData?.user?.name || ''}
        width={40}
        height={40}
      />
      <div className="w-full text-sm">
        {openEditComment ? (
          <EditComment
            data={commentData}
            members={members}
            onCancel={() => {
              setOpenEditComment(false)
            }}
            editingCommentIds={editingCommentIds}
            addEditingCommentId={addEditingCommentId}
            removeEditingCommentId={removeEditingCommentId}
          />
        ) : (
          <div>
            <div className="group relative mb-1.5 w-full rounded-[10px] bg-white-100 px-3 py-2.5 dark:bg-black-300">
              <div className="mb-1.5 flex items-center">
                <UserDetailsHoverCard username={commentData.user.username}>
                  <Link href={`/@${commentData.user.username}`} className="flex items-center">
                    <span className="font-bold">{commentData.user.name}</span>
                    {isAdminOrCreatorActor && <Verified16Icon className="ml-0.5 text-community-primary" />}
                  </Link>
                </UserDetailsHoverCard>
                <span className="text-xs text-black-100">
                  &nbsp;•&nbsp;
                  <AdaptiveTooltipProvider>
                    <AdaptiveTooltip>
                      <AdaptiveTooltipTrigger asChild>
                        <span className="cursor-pointer select-none text-xs text-black-100">{createdTimeAgo}</span>
                      </AdaptiveTooltipTrigger>
                      <AdaptiveTooltipContent>{createdAtLong}</AdaptiveTooltipContent>
                    </AdaptiveTooltip>
                  </AdaptiveTooltipProvider>
                  {commentData.edited && <>&nbsp;•&nbsp;Edited</>}
                </span>
              </div>
              <div className="relative">{renderedText}</div>
              {shouldShowDropdown && (
                <div className="absolute right-2 top-2">
                  <DropdownMenu open={actionsDropdownOpen} onOpenChange={setActionsDropdownOpen}>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="inline"
                        className={cn(
                          'h-6 w-6 p-0 text-black-100 hover:text-black-200 md:opacity-0 md:group-hover:opacity-100',
                          actionsDropdownOpen && 'md:opacity-100',
                        )}
                        aria-label="Comment options"
                      >
                        <MoreHorizontal20Icon className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      {canEditComment && (
                        <DropdownMenuItem onClick={() => setOpenEditComment(true)} className="cursor-pointer">
                          Edit comment
                        </DropdownMenuItem>
                      )}
                      {canDeleteComment && (
                        <DropdownMenuItem
                          onClick={() => setShowDeleteConfirmation(true)}
                          className="cursor-pointer text-red-600 focus:text-red-600 dark:text-red-400 dark:focus:text-red-400"
                        >
                          Delete comment
                        </DropdownMenuItem>
                      )}
                      {canReportComment && (
                        <DropdownMenuItem
                          onClick={handleReportComment}
                          disabled={isReporting}
                          className="cursor-pointer text-orange-600 focus:text-orange-600 dark:text-orange-400 dark:focus:text-orange-400"
                        >
                          {isReporting ? 'Reporting...' : 'Report comment'}
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              )}
            </div>

            <div className="ml-[0.875rem] flex items-center gap-3 text-xs font-semibold text-black-100">
              <div className="flex items-center gap-2">
                <Button
                  className={cn(
                    'text-xs font-semibold transition-colors hover:text-black-200',
                    liked ? 'text-primary' : 'text-black-100',
                  )}
                  disabled={!isUserAllowedToPost || isLikeButtonDisabled}
                  variant="inline"
                  onClick={handleLikeClick}
                  aria-label="Like"
                >
                  {liked ? <SVGHeartFilled className="text-community-primary" /> : <SVGHeart />}
                </Button>
                <div className="text-xs font-semibold text-black-100">{likeCount()}</div>
              </div>
              <Button
                className="text-xs transition-colors hover:text-black-200"
                variant="inline"
                onClick={handleReplyClick}
                disabled={!isUserAllowedToPost}
              >
                Reply
              </Button>
            </div>

            {visibleNewReply && (
              <div className="mt-3">
                <NewComment
                  isReply={true}
                  topLevelMessage={parentMessage}
                  secondaryLevelMessage={commentData as IFeed}
                  handleCancel={handleCancel}
                  members={members}
                  commentType="reply"
                  parentPermalink={`/post/${parentMessage?.permalink || parentMessage?.id}`}
                  addMentionByDefault={true}
                  scrollToBottom={() => {}}
                  portal={true}
                  focus
                />
              </div>
            )}
          </div>
        )}
        {repliesToRender?.length > 0 && (
          <div className="space-y-5 pt-5">
            {repliesToRender.map((reply) => {
              return (
                <Comment
                  key={reply.id}
                  className="ml-[0.875rem]"
                  commentData={reply}
                  members={members}
                  parentMessage={parentMessage}
                  replies={[]}
                  editingCommentIds={editingCommentIds}
                  addEditingCommentId={addEditingCommentId}
                  removeEditingCommentId={removeEditingCommentId}
                />
              )
            })}
          </div>
        )}
        {remainingRepliesToShow > 0 && (
          <div>
            <Button
              className="ml-[0.875rem] mt-3 text-xs font-semibold text-community-primary hover:text-community-secondary"
              disabled={!isUserAllowedToPost || isLikeButtonDisabled}
              variant="inline"
              onClick={handleViewMoreReplies}
            >
              View {remainingRepliesToShow} more&nbsp;
              {remainingRepliesToShow == 1 ? 'reply' : 'replies'}
            </Button>
          </div>
        )}
      </div>
      <ConfirmModal
        title="Delete comment"
        open={showDeleteConfirmation}
        onCancel={() => setShowDeleteConfirmation(false)}
        onConfirm={handleDeleteComment}
        loading={isDeleting}
      >
        Are you sure you want to delete this comment? This action cannot be undone.
      </ConfirmModal>
    </div>
  )
}
