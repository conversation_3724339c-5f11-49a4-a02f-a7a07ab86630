import _groupBy from 'lodash/groupBy'
import { useSearchParams } from 'next/navigation'
import React, { CSSProperties, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { StreamMessage, useChannelStateContext } from 'stream-chat-react'

import { ChevronLeft24Icon } from '../icons'
import { Comment } from './comment'
import { PostDetailContent } from './post-detail-content'
import { upsertFeedTrack } from '@memberup/shared/src/services/apis/feed-track.api'
import { FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import NewComment from '@/components/feed/new-comment'
import { PostHeader } from '@/components/feed/post-header'
import { PostSocialBar } from '@/components/feed/post-social-bar'
import { Button, Separator } from '@/components/ui'
import { ScrollArea } from '@/components/ui/scroll-area/ScrollArea'
import { useStore } from '@/hooks/useStore'
import { scrollIntoViewWithOffset } from '@/lib/ui'
import { cn } from '@/lib/utils'
import useAppStreamMessageListenerNew from '@/memberup/components/hooks/use-app-stream-message-listener-new'
import { addViewedFeed } from '@/memberup/store/features/feedTrackSlice'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { RootState, useAppDispatch } from '@/memberup/store/store'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { IMembership, IUser } from '@/shared-types/interfaces'

interface PostDetailProps {
  closeModal?: () => void
  membership: IMembership
  feed: StreamMessage
  styleOverride?: CSSProperties
  isPostPage?: boolean
  setIsSubmittingComment?: (isSubmitting: boolean) => void
  onPinMessage: (message: StreamMessage) => Promise<void>
  onUnpinMessage: (message: StreamMessage) => Promise<void>
  editingCommentIds: string[]
  addEditingCommentId: (commentId: string) => void
  removeEditingCommentId: (commentId: string) => void
}

export function PostDetail({
  closeModal,
  membership,
  feed: message,
  styleOverride = null,
  isPostPage = false,
  setIsSubmittingComment = undefined,
  onPinMessage,
  onUnpinMessage,
  editingCommentIds,
  addEditingCommentId,
  removeEditingCommentId,
}: PostDetailProps) {
  const dispatch = useAppDispatch()
  const rootEleRef = useRef(null)
  const params = useSearchParams()
  const { channel: streamChatChannel } = useChannelStateContext()
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)
  const initialMembersRef = useRef<{ [key: string]: IUser } | null>(null)
  const initialCommentsLoaded = useRef(false)

  const INITIAL_COMMENTS_FETCH_PAGE_SIZE = 100
  const SHOW_MORE_COMMENTS_FETCH_PAGE_SIZE = 100

  const stickyItemsOffset = useStore((state) => state.ui.stickyItemsOffset)

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  const { messages, updated, fetchMessages } = useAppStreamMessageListenerNew(
    true,
    'all',
    streamChatChannel,
    message,
    { feed_status: { $in: [FEED_STATUS_ENUM.active, FEED_STATUS_ENUM.approved] } },
    {
      limit: INITIAL_COMMENTS_FETCH_PAGE_SIZE,
      sort: { hierarchy_order: 1, created_at: 1 },
    },
  )

  const [isLoadingAllComments, setIsLoadingAllComments] = useState(false)

  const members = useAppSelector((state) => selectMembersMap(state))
  const feedTrackIds = useSelector((state: RootState) => state.feedTrack.feedTrackIds)
  const isPostOwner = message.user.id === userProfile?.user_id
  const feed = message

  const latestCommentTrackDate = feedTrackIds?.[feed.id] ? feedTrackIds[feed.id] : null
  const hasCommentsUnread = message.latest_comment_timestamp > latestCommentTrackDate

  const hasPostBeenRead = feedTrackIds?.[feed.id] || isPostOwner ? true : false

  useEffect(() => {
    if (!initialMembersRef?.current && Object.keys(members).length > 0) {
      initialMembersRef.current = members
    }
  }, [members])

  const scrollToBottom = () => {
    const appScrollArea = document.getElementById('app-scroll-area')
    if (!appScrollArea) {
      return
    }

    appScrollArea.scrollTo({
      top: appScrollArea.scrollHeight - appScrollArea.clientHeight,
      behavior: 'smooth',
    })
  }

  useEffect(() => {
    /* go to comment after clicking comment notification */
    const commentId = params.get('comment_id')

    if (commentId && messages?.results?.length > 0 && !initialCommentsLoaded.current) {
      setTimeout(() => {
        initialCommentsLoaded.current = true
        const commentElement = document.getElementById(commentId as string)

        if (commentElement) {
          scrollIntoViewWithOffset(commentElement, 100)
          const style = document.createElement('style')

          style.innerHTML = `
            @keyframes highlight {
              from {
                background-color: rgb(59 130 246 / 0.3);
              }
              to {
                background-color: transparent;
              }
            }
          `

          document.head.appendChild(style)

          commentElement.style.animation = 'highlight 2s'

          const handleAnimationEnd = () => {
            document.head.removeChild(style)
          }

          commentElement.addEventListener('animationend', handleAnimationEnd)

          // Cleanup function
          return () => {
            commentElement?.removeEventListener('animationend', handleAnimationEnd)
          }
        }
      }, 2000)
    }
  }, [messages])

  useEffect(() => {
    if (isLoadingAllComments) {
      if (messages.next) {
        fetchMessages(false, SHOW_MORE_COMMENTS_FETCH_PAGE_SIZE)
      } else {
        setIsLoadingAllComments(false)
        setTimeout(() => {
          // scrollToBottom()
        })
      }
    }
  }, [messages])

  useEffect(() => {
    if (!user) {
      return
    }

    if (!hasPostBeenRead) {
      const now = Math.floor(Date.now() / 1000)
      upsertFeedTrack({ feed_id: feed.id, updated_at: now })
      dispatch(addViewedFeed({ id: feed.id, updatedAt: now }))
    }
  }, [hasPostBeenRead, feed.id, dispatch, user])

  const renderedComments = useMemo(() => {
    function isSecondLevelComment(item: any) {
      return item.hierarchy_order?.includes('.')
    }

    function isTopLevelComment(item: any) {
      return !isSecondLevelComment(item)
    }

    let temp = messages.results.length
      ? messages.next
        ? messages.results.slice(0, messages.results.length - 1)
        : messages.results
      : []

    // Some replies were converted from reactions and we need to use created_at_ext instead.
    temp = temp.map((r) => {
      const newObj = { ...r }
      newObj.created_at = r.createdAt || r.created_at_ext || r.created_at
      return newObj
    })

    let oldTopLevelComments = temp.filter((c) => !c.reply_parent_id && !c.hierarchy_order)
    oldTopLevelComments = oldTopLevelComments.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    )

    let newCommentsWithHierarchy = temp.filter((c) => c.reply_parent_id && c.hierarchy_order)

    newCommentsWithHierarchy = newCommentsWithHierarchy.sort((a, b) =>
      a.hierarchy_order.localeCompare(b.hierarchy_order),
    )
    const allComments = oldTopLevelComments.concat(newCommentsWithHierarchy)

    if (!allComments.length) return null

    const processedCommentsIds = new Set()
    const commentsToRender = []
    for (const comment of allComments) {
      if (!comment.reply_parent_id) {
        processedCommentsIds.add(comment.id)
        commentsToRender.push(comment)
      } else if (comment.reply_parent_id === message.id || processedCommentsIds.has(comment.reply_parent_id)) {
        processedCommentsIds.add(comment.id)
        commentsToRender.push(comment)
      }
    }

    const repliesMap = _groupBy(commentsToRender, 'reply_parent_id')

    if (!commentsToRender.length) return null

    return (
      <>
        <Separator className="mb-5 bg-grey-200 dark:bg-grey-900" />
        <div className="space-y-5 px-4 pb-5 md:px-6">
          {commentsToRender.map(
            (comment) =>
              isTopLevelComment(comment) && (
                <div key={comment.id}>
                  <Comment
                    commentData={comment}
                    members={initialMembersRef?.current}
                    parentMessage={message as unknown as IFeed}
                    replies={repliesMap[comment.id]}
                    editingCommentIds={editingCommentIds}
                    addEditingCommentId={addEditingCommentId}
                    removeEditingCommentId={removeEditingCommentId}
                  />
                </div>
              ),
          )}
        </div>
      </>
    )
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [(feed as unknown as IFeed)?.feed_status, messages.results, updated, members])

  return (
    <>
      <div
        className={cn(
          'post-detail w-full md:bg-white-500 dark:md:bg-black-500',
          isPostPage
            ? 'rounded-base bg-transparent md:bg-white-500'
            : 'h-full overflow-hidden bg-white-500 dark:bg-black-700 md:h-auto md:max-h-[90vh] md:rounded-base md:bg-white-500',
        )}
        data-testid="post-detail"
        style={styleOverride || {}}
      >
        <div className={cn('feed-card-content', isPostPage ? '' : 'h-full md:h-auto')}>
          <div
            className={cn(
              'post-detail-content-inner flex w-full flex-col',
              isPostPage ? '' : 'h-screen md:h-auto md:max-h-[90vh]',
            )}
          >
            <div
              className={cn(
                'post-detail-header-container sticky left-0 z-40 flex flex-shrink-0 items-stretch bg-transparent',
                isPostPage && 'dark:bg-black-700 md:bg-grey-100 md:dark:bg-black-700',
              )}
              style={{
                top: isPostPage ? `${stickyItemsOffset}px` : '0',
              }}
            >
              <div className="post-detail-header-container-inner flex w-full flex-shrink-0 items-stretch rounded-tl-base rounded-tr-base bg-white-500 p-4 dark:bg-black-700 md:p-5 md:dark:bg-black-500">
                {isPostPage && <div className="absolute -top-5 left-0 h-5 w-full bg-grey-100 dark:bg-black-700" />}
                {!isPostPage && (
                  <div className="md:hidden">
                    <Button
                      className="mr-3 mt-1.5 text-black-200 dark:text-black-100"
                      onClick={closeModal}
                      variant="inline"
                    >
                      <ChevronLeft24Icon />
                    </Button>
                  </div>
                )}
                <PostHeader
                  feed={feed as unknown as IFeed}
                  isSinglePost={true}
                  isPostPage={isPostPage}
                  userData={members[feed.user.id]}
                  onPinMessage={onPinMessage as unknown as (feed: IFeed) => void}
                  onUnpinMessage={onUnpinMessage as unknown as (feed: IFeed) => void}
                  membership={membership}
                />
              </div>
            </div>

            {isPostPage ? (
              // On the page, content scrolls with AppLayout's ScrollArea
              <div ref={rootEleRef} className="post-details-container w-full">
                <PostDetailContent feed={feed as unknown as IFeed} />
                <PostSocialBar
                  className="mt-4 pb-5 md:px-5"
                  message={feed as unknown as IFeed}
                  isDetailView={true}
                  hasCommentsUnread={hasCommentsUnread}
                  members={members}
                  readonly={!isUserAllowedToPost}
                />
                {renderedComments}
              </div>
            ) : (
              // In modal, use different approaches for mobile vs desktop
              <>
                {/* Mobile: Use ScrollArea */}
                <ScrollArea className="min-h-0 flex-1 overflow-hidden md:hidden">
                  <div ref={rootEleRef} className="post-details-container w-full">
                    <PostDetailContent feed={feed as unknown as IFeed} />
                    <PostSocialBar
                      className="mt-4 pb-5 md:px-5"
                      message={feed as unknown as IFeed}
                      isDetailView={true}
                      hasCommentsUnread={hasCommentsUnread}
                      members={members}
                      readonly={!isUserAllowedToPost}
                    />
                    {renderedComments}
                  </div>
                </ScrollArea>

                {/* Desktop: Use flexbox with native scrolling */}
                <div className="hidden md:flex md:min-h-0 md:flex-1 md:flex-col">
                  <div
                    ref={rootEleRef}
                    className="post-details-container w-full flex-1 overflow-y-auto"
                    style={{ scrollbarGutter: 'stable' }}
                  >
                    <PostDetailContent feed={feed as unknown as IFeed} />
                    <PostSocialBar
                      className="mt-4 pb-5 md:px-5"
                      message={feed as unknown as IFeed}
                      isDetailView={true}
                      hasCommentsUnread={hasCommentsUnread}
                      members={members}
                      readonly={!isUserAllowedToPost}
                    />
                    {renderedComments}
                  </div>
                </div>
              </>
            )}

            <div
              className={cn(
                'card-new-comment z-30 w-full flex-shrink-0 border-t border-grey-200 dark:border-grey-900',
                isPostPage && 'sticky bottom-0 bg-grey-100 dark:bg-black-700 md:bottom-5',
                !isPostPage && 'md:max-h-[40%]', // Limit comment box to 40% of modal height
              )}
            >
              {isPostPage && <div className="absolute -bottom-5 left-0 h-5 w-full bg-grey-100 dark:bg-black-700" />}
              {isUserAllowedToPost && (
                <div
                  className={cn(
                    'w-full rounded-bl-base rounded-br-base px-4 py-4',
                    isPostPage
                      ? 'bg-white-700 dark:bg-black-700 md:bg-white-500 dark:md:bg-black-500'
                      : 'dark:bg-black-700 md:overflow-y-auto md:bg-white-500 md:px-6 dark:md:bg-black-500',
                  )}
                  data-testid="new-comment-container"
                >
                  <NewComment
                    isReply={false}
                    topLevelMessage={feed as unknown as IFeed}
                    members={(initialMembersRef?.current || []) as { [key: string]: IUser }}
                    addMentionByDefault={false}
                    parentPermalink={`/post/${feed?.permalink || feed?.id}`}
                    scrollToBottom={scrollToBottom}
                    setIsSubmittingComment={setIsSubmittingComment}
                    portal={true}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
