export function Unlink16Icon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.53454 7.30691C4.91732 7.68969 4.91732 8.31031 4.53454 8.69309C3.76897 9.45866 3.76897 10.6999 4.53454 11.4655C5.25981 12.1907 6.412 12.2289 7.18221 11.58L7.3924 11.3893C7.77714 11.0846 8.33765 11.11 8.69309 11.4655C9.07588 11.8482 9.07588 12.4689 8.69309 12.8516C7.16195 14.3828 4.67949 14.3828 3.14835 12.8516C1.61722 11.3205 1.61722 8.83805 3.14835 7.30691C3.53114 6.92412 4.15175 6.92412 4.53454 7.30691ZM4.46447 3.05025L12.9497 11.5355C13.3403 11.9261 13.3403 12.5592 12.9497 12.9497C12.5592 13.3403 11.9261 13.3403 11.5355 12.9497L3.05025 4.46447C2.65973 4.07394 2.65973 3.44078 3.05025 3.05025C3.44078 2.65973 4.07394 2.65973 4.46447 3.05025ZM12.8516 3.14835C14.3828 4.67949 14.3828 7.16195 12.8516 8.69309C12.4689 9.07588 11.8482 9.07588 11.4655 8.69309C11.0827 8.31031 11.0827 7.68969 11.4655 7.30691C12.231 6.54134 12.231 5.30011 11.4655 4.53454C10.7402 3.80926 9.588 3.77109 8.81779 4.42002L8.6076 4.6107C8.22286 4.91537 7.66235 4.88998 7.30691 4.53454C6.92412 4.15175 6.92412 3.53114 7.30691 3.14835C8.83805 1.61722 11.3205 1.61722 12.8516 3.14835Z"
        fill="currentColor"
      />
    </svg>
  )
}
