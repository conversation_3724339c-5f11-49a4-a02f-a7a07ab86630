export function MemberupM24Icon(props: any) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <g>
        <path
          d="M 23.13003,14.348275 C 23.022775,13.539724 22.765397,12.973881 22.509332,12.410892 22.180317,11.687562 22.143386,11.847103 21.324275,10.398275 20.904045,9.6549677 20.737369,9.3008948 20.158055,8.3103867 19.941946,7.940822 19.777839,7.6698589 19.744275,7.6143991 18.922367,6.2584761 18.511384,5.5805546 18.164218,5.2255683 17.203375,4.2429546 16.044632,3.9456714 15.813055,3.8900461 c -0.09949,-0.0239 -1.024089,-0.2379932 -2.031453,-0.094018 -0.20766,0.029648 -0.663908,0.1095896 -0.79,0.1316571 C 12.525822,4.0092882 12.14298,4.0829054 11.881835,4.134615 11.775265,4.128375 11.668695,4.12204 11.562068,4.115795 11.31759,4.0217831 11.073056,3.9276852 10.828521,3.8336673 10.425244,3.7286629 9.7927873,3.6141637 9.0227657,3.6831962 7.9583778,3.7786241 7.2133005,4.1725852 7.0101496,4.285086 6.0931788,4.7932146 5.5583192,5.476633 5.2985097,5.8650972 4.916039,6.5234457 4.533574,7.1817999 4.1511033,7.8401484 3.1198566,9.6204908 2.299541,11.048655 1.7246334,12.034728 c -0.1059251,0.181689 -0.3284265,0.562704 -0.5266993,1.109768 -0.1280731,0.353559 -0.37047871,1.040585 -0.37622448,1.93744 -0.007911,1.24699 0.44642258,2.159257 0.54551678,2.351163 0.6635149,1.285748 1.7141577,1.892861 2.163082,2.144359 0.463577,0.259661 1.6480403,0.901365 3.2164698,0.714825 0.2090874,-0.02489 1.3122903,-0.169987 2.3888364,-0.959302 0.7128266,-0.522633 1.1451574,-1.130602 1.3543594,-1.429535 0.212797,-0.304298 0.292425,-0.473771 0.846453,-1.448371 0.299675,-0.527199 0.548833,-0.956847 0.714824,-1.241396 0.526686,0.921629 1.053314,1.843314 1.58,2.765057 0.178663,0.308237 0.485188,0.75792 0.978082,1.185 0.834751,0.723386 1.698385,0.944232 2.069069,1.034592 0.233917,0.05697 1.233347,0.283121 2.482849,0 0.357326,-0.08094 1.108226,-0.259034 1.899767,-0.827617 0.232661,-0.167133 0.720589,-0.54889 1.166277,-1.185057 0.104001,-0.148467 0.437411,-0.641646 0.677151,-1.373081 0.34905,-1.06513 0.288373,-1.991611 0.225755,-2.464126 z m -1.908786,1.711686 c -0.114676,0.378446 -0.409784,1.11342 -1.147384,1.692849 -0.27981,0.219818 -0.810834,0.564473 -1.56122,0.677151 -0.230493,0.03459 -0.992751,0.123979 -1.824534,-0.244478 C 16.145152,17.944887 15.794675,17.607653 15.597118,17.41432 15.315024,17.138105 15.128027,16.875134 14.97642,16.661936 14.73982,16.329155 14.568634,16.02771 14.449734,15.796647 13.566864,14.080965 12.529818,12.44925 11.571829,10.774496 11.341735,10.372304 10.970253,9.725748 10.499677,8.9217633 10.443338,8.8351715 10.357717,8.7173394 10.236363,8.5925948 10.151712,8.5055749 10.082073,8.4340468 9.9731059,8.3668454 9.8835459,8.3117167 9.716413,8.2087941 9.4934549,8.2163744 9.2591948,8.2243658 9.0944022,8.3497725 8.9855489,8.4327111 8.850153,8.535805 8.7634471,8.6479747 8.7128163,8.7242519 8.3295179,9.351868 7.9501581,9.9819843 7.5748511,10.614612 c -0.619328,1.043725 -1.2274453,2.093957 -1.8245859,3.150582 -0.1348194,0.228837 -0.2696388,0.457675 -0.4043726,0.686626 -0.2012673,0.272048 -0.251567,0.621041 -0.1222445,0.91221 0.1568869,0.353502 0.5090476,0.465495 0.5830759,0.48907 0.076026,0.02415 0.2978653,0.09133 0.5642506,0.0094 0.3171244,-0.09749 0.4763802,-0.338147 0.5173072,-0.404361 0.2624581,-0.435128 0.469776,-0.788687 0.6018612,-1.015755 0.6387926,-1.098048 0.9217427,-1.646938 1.6270916,-2.859052 0.1392774,-0.239511 0.2528685,-0.432216 0.3197673,-0.545522 0.042354,0.03916 0.1058851,0.100006 0.1786633,0.17872 0.1185,0.128204 0.1967008,0.233747 0.225755,0.272676 0.3707978,0.498659 0.4259378,0.509904 0.5736628,0.752384 0.156916,0.257549 0.380559,0.624808 0.357441,1.072151 -0.01267,0.244192 -0.0963,0.461214 -0.103431,0.479651 -0.03608,0.09184 -0.0629,0.133055 -0.17872,0.329128 -0.07826,0.132428 -0.264285,0.447857 -0.420458,0.733091 -0.1098236,0.200639 -0.086991,0.170957 -0.2002397,0.376677 -0.1571438,0.285575 -0.1867117,0.316285 -0.3197673,0.564302 -0.099435,0.185227 -0.1099379,0.218334 -0.2104574,0.399851 0,0 -0.09601,0.173298 -0.1939039,0.333695 -0.5159544,0.845026 -0.9969753,1.22256 -0.9969753,1.22256 C 7.6521957,18.14233 7.1574183,18.279267 6.9447919,18.335777 6.4022834,18.480078 5.9671042,18.456446 5.8538557,18.448626 5.1033215,18.397082 4.5739588,18.089873 4.386682,17.978394 3.4277169,17.407242 2.5770913,16.23988 2.6373802,15.025312 2.6598643,14.572033 2.8055862,14.183997 2.9947809,13.680372 3.2409367,13.025083 3.5196514,12.577739 3.6719547,12.326012 4.2920818,11.300839 5.0959124,9.971938 5.7598377,8.6863616 5.79065,8.626655 6.2112564,7.8116707 6.860198,6.880601 7.0518757,6.6056365 7.3001206,6.267883 7.734906,5.9777635 7.8144768,5.9246383 8.1342441,5.7169551 8.6095569,5.5827179 8.9308654,5.4920392 9.4925416,5.3870331 10.161359,5.5262592 c 0.176608,0.036812 0.667619,0.1543868 1.185057,0.5078775 0.587306,0.4012902 0.915064,0.9058399 1.029797,1.0862898 0.01358,0.02132 0.0085,0.013825 0.380901,0.6348654 0.06507,0.1085051 0.274559,0.4576691 0.338604,0.5642563 0.149495,0.2894516 0.384383,0.7337866 0.69125,1.2696623 0.337919,0.5900455 0.401165,0.6592275 0.719448,1.2131975 0.430789,0.749758 0.487756,0.924083 0.921685,1.70221 0.174782,0.313546 0.444661,0.785891 0.799419,1.35904 0.0939,0.214682 0.213596,0.45368 0.366802,0.705406 0.141446,0.232548 0.286375,0.434443 0.423198,0.606599 0.09441,0.06764 0.377361,0.251328 0.761744,0.225755 0.129003,-0.0086 0.421143,-0.02803 0.620698,-0.253896 0.250414,-0.283463 0.17267,-0.685484 0.150523,-0.780581 -0.02043,-0.08751 -0.04315,-0.126891 -0.338604,-0.658371 -0.08271,-0.148696 -0.140762,-0.253268 -0.180205,-0.324677 -0.0026,-0.0045 -0.0051,-0.0091 -0.0079,-0.01387 -0.31963,-0.560764 -0.75139,-1.348709 -1.373116,-2.44529 -0.370283,-0.653177 -0.936127,-1.6418758 -1.768081,-3.0660162 -0.174154,-0.3533252 -0.37348,-0.7252987 -0.601918,-1.1097672 -0.178378,-0.3002 -0.35824,-0.5807413 -0.536104,-0.8417152 0.237342,-0.1075062 0.657458,-0.2586451 1.194475,-0.2492382 0.508363,0.00891 0.88093,0.1574692 0.996918,0.2069355 0.355272,0.15139 0.588105,0.3464127 0.733547,0.4702383 0.262173,0.2230837 0.444546,0.4494209 0.564245,0.6207093 0.435527,0.7235521 0.785776,1.3083746 1.015755,1.6928431 0.641076,1.0718884 0.930362,1.5624416 1.20378,2.0314186 0.420458,0.721046 0.677265,1.174668 0.921685,1.617673 0.204407,0.370627 0.37154,0.681889 0.489069,0.902792 0.16662,0.285805 0.428507,0.821737 0.48907,1.542384 0.0552,0.657515 -0.0834,1.157316 -0.131686,1.316743 z"
          strokeWidth="0.570809"
          fillOpacity="1"
          fill="currentColor"
        />
      </g>
    </svg>
  )
}
