export function QuestionMark24Icon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        d="m 8.25,7.50001 c 0,-1.65468 1.34531,-3 3,-3 h 1.5 c 1.6547,0 3,1.34532 3,3 v 0.16876 c 0,1.02187 -0.5203,1.97342 -1.3781,2.52182 l -1.9782,1.2704 c -1.1812,0.7593 -1.8937,2.0671 -1.8937,3.4687 v 0.0703 c 0,0.8297 0.6703,1.5 1.5,1.5 0.8297,0 1.5,-0.6703 1.5,-1.5 v -0.0656 c 0,-0.3844 0.1969,-0.7406 0.5156,-0.9469 l 1.9781,-1.2703 C 17.7094,11.61099 18.75,9.71249 18.75,7.66877 V 7.50001 c 0,-3.31406 -2.6859,-6 -6,-6 h -1.5 c -3.31406,0 -6,2.68594 -6,6 0,0.82969 0.67031,1.5 1.5,1.5 0.82969,0 1.5,-0.67031 1.5,-1.5 z M 12,22.49999 c 0.4973,0 0.9742,-0.1975 1.3258,-0.5492 0.3517,-0.3516 0.5492,-0.8285 0.5492,-1.3258 0,-0.4973 -0.1975,-0.9742 -0.5492,-1.3258 -0.3516,-0.3516 -0.8285,-0.5492 -1.3258,-0.5492 -0.4973,0 -0.9742,0.1976 -1.3258,0.5492 -0.3517,0.3516 -0.5492,0.8285 -0.5492,1.3258 0,0.4973 0.1975,0.9742 0.5492,1.3258 0.3516,0.3517 0.8285,0.5492 1.3258,0.5492 z"
        fill="currentColor"
      />
    </svg>
  )
}
