import * as VisuallyHidden from '@radix-ui/react-visually-hidden'
import React, { useState } from 'react'

import { ImageCropper } from '@/components/images/image-cropper'
import { Button } from '@/components/ui'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogInner,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog'
import { getCroppedImg } from '@/lib/client/images'
import { CropArea } from '@/shared-types/types'

export function ImageCropperDialog({
  aspectRatio,
  cropShape = 'rect',
  file,
  minWidth,
  minHeight,
  onCropComplete,
  onClose,
  open,
  url,
  variant = 'default',
}: {
  aspectRatio: number
  cropShape?: 'rect' | 'round'
  file: File
  minWidth: number
  minHeight: number
  onCropComplete: (cropArea: CropArea, croppedImageBlob?: string) => void
  onClose: () => void
  open: boolean
  url: string
  variant?: 'default' | 'community-primary'
}) {
  const [cropArea, setCropArea] = useState<CropArea>({ x: 0, y: 0, width: 0, height: 0 })

  const onComplete = async () => {
    const croppedImage = await getCroppedImg(url, cropArea)
    onCropComplete(cropArea, croppedImage)
  }

  return (
    <Dialog open={open} onOpenChange={() => onClose()}>
      <DialogContent
        className="z-[10000] [&_.dialog-footer]:border-t [&_.dialog-footer]:border-grey-200 [&_.dialog-footer]:dark:border-grey-900"
        overlay={<DialogOverlay className="z-[9999]" />}
      >
        <DialogHeader className="w-full border-b border-grey-200 p-5 dark:border-grey-900 [&_.dialog-title]:mb-0 [&_.dialog-title]:leading-10">
          <DialogTitle>Adjust your image</DialogTitle>
          <VisuallyHidden.Root>
            <DialogDescription>Rearrange and zoom in on your image</DialogDescription>
          </VisuallyHidden.Root>
        </DialogHeader>
        {Boolean(url) && (
          <DialogInner className="mb-0 flex justify-center pt-8">
            <div className="flex w-full max-w-96 justify-center">
              <ImageCropper
                aspectRatio={aspectRatio}
                cropShape={cropShape}
                file={file}
                minWidth={minWidth}
                minHeight={minHeight}
                setCropArea={setCropArea}
                onClose={onClose}
                open={open}
                url={url}
                variant={variant}
              />
            </div>
          </DialogInner>
        )}
        <DialogFooter className="border-t border-grey-200 px-5 pt-5 dark:border-grey-900">
          <Button className="w-full" onClick={onComplete} variant={variant}>
            Save
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
