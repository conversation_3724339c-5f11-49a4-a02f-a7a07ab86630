import React, { useState } from 'react'
import Cropper from 'react-easy-crop'

import { AspectRatio } from '@/components/ui'
import { Slider } from '@/components/ui/slider'
import { CropArea } from '@/shared-types/types'

export function ImageCropper({
  aspectRatio,
  cropShape = 'rect',
  minWidth,
  minHeight,
  setCropArea,
  setImageDimensions,
  url,
  variant = 'default',
}: {
  aspectRatio: number
  cropShape?: 'rect' | 'round'
  file: File
  minWidth: number
  minHeight: number
  onClose: () => void
  open: boolean
  setCropArea: (cropArea: CropArea) => void
  setImageDimensions?: (imageDimensions: { width: number; height: number }) => void
  url: string
  variant?: 'default' | 'community-primary'
}) {
  const [crop, setCrop] = useState({ x: 0, y: 0 })
  const [zoom, setZoom] = useState(1)
  const [maxZoom, setMaxZoom] = useState(1)

  const onCropComplete = (croppedArea: CropArea) => {
    setCropArea(croppedArea)
  }

  return (
    <div className="image-cropper-image-wrapper w-full max-w-96 px-0 sm:px-6">
      <style>
        {`
          @media (min-height: 600px) and (max-height: 700px) {
            .image-cropper-image-wrapper {
              max-width: 19rem !important;
            }
          }

          @media (max-height: 600px) {
            .image-cropper-image-wrapper {
              max-width: 17.5rem !important;
            }
          }
        `}
      </style>
      <AspectRatio ratio={aspectRatio}>
        <Cropper
          image={url}
          crop={crop}
          zoom={zoom}
          aspect={aspectRatio}
          cropShape={cropShape}
          maxZoom={maxZoom}
          showGrid={false}
          onCropChange={setCrop}
          onCropComplete={onCropComplete}
          onZoomChange={setZoom}
          onMediaLoaded={(mediaSize) => {
            const calculatedMaxZoom = Math.min(mediaSize.naturalWidth / minWidth, mediaSize.naturalHeight / minHeight)

            setMaxZoom(calculatedMaxZoom)
            setImageDimensions && setImageDimensions({ width: mediaSize.naturalWidth, height: mediaSize.naturalHeight })
          }}
        />
      </AspectRatio>
      <Slider
        className="my-8"
        min={1}
        max={maxZoom}
        step={0.01}
        value={[zoom]}
        onValueChange={(value) => setZoom(value[0])}
        variant={variant}
      />
    </div>
  )
}
