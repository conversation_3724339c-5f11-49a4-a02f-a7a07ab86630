import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/lib/utils'

const primaryBlurredBackgroundVariants = cva(
  'absolute left-1/2 -ml-[150px] bg-primary-200/[0.17] rounded-full shadow-[0px_0px_200px_140px_rgba(123,0,255,0.08),0px_0px_600px_300px_rgba(123,0,255,0.08)]',
  {
    variants: {
      variant: {
        default: 'w-[300px] h-[339px] blur-[100px]',
        small: 'w-[318px] h-[360px]',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

export interface PrimaryBlurredBackgroundProps
  extends React.ButtonHTMLAttributes<HTMLDivElement>,
    VariantProps<typeof primaryBlurredBackgroundVariants> {}

export function PrimaryBlurredBackground({ className, variant }: PrimaryBlurredBackgroundProps) {
  return <div className={cn(primaryBlurredBackgroundVariants({ className, variant }))} />
}
