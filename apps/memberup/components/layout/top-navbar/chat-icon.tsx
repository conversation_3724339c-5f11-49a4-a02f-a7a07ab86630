import { Chat24Icon } from '@/components/icons/24px/chat-24-icon'
import { NotificationsIndicator } from '@/components/ui/notifications-indicator'
import { cn } from '@/lib/utils'
import { selectTotalUnreadCount } from '@/memberup/store/features/inboxSlice'
import { useAppSelector } from '@/memberup/store/hooks'

export function ChatIcon({ small }: { small?: boolean }) {
  const totalUnreadInboxMessagesCount = useAppSelector((state) => selectTotalUnreadCount(state))

  return (
    <div>
      <NotificationsIndicator count={totalUnreadInboxMessagesCount} small={small}>
        <Chat24Icon className={cn(small ? 'h-[1.125rem] w-[1.125rem]' : '')} />
        <span className="sr-only">Chat</span>
      </NotificationsIndicator>
    </div>
  )
}
