import Link from 'next/link'

import { Settings24Icon, User24Icon, UserAdd24Icon } from '@/components/icons'
import { Help20Icon } from '@/components/icons/20px/help-20-icon'
import { Logout20Icon } from '@/components/icons/20px/logout-20-icon'
import { UserAdd20Icon } from '@/components/icons/20px/user-add-20-icon'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Separator } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { useLogout } from '@/lib/auth'
import { getFullName } from '@/lib/formatting'
import { cn } from '@/lib/utils'

const UserMenuItem = ({
  children,
  className,
  href,
  onClick,
}: {
  children: React.ReactNode
  className?: string
  href?: string
  onClick?: () => void
}) => {
  const buttonClassName =
    'flex h-10 items-center px-2.5 bg-transparent w-full text-sm font-medium hover:bg-white-100 dark:hover:bg-black-300 rounded-base transition-colors'

  if (href) {
    return (
      <Link className={cn(buttonClassName, className)} href={href} onClick={onClick}>
        {children}
      </Link>
    )
  }

  return (
    <button className={cn(buttonClassName, className)} onClick={onClick}>
      {children}
    </button>
  )
}

export function UserMenu({ onItemClick }: { onItemClick?: () => void }) {
  const user = useStore((state) => state.auth.user)
  const logout = useLogout()

  return (
    <>
      <div className="p-[0.1875rem]">
        <UserMenuItem href={`/@${user.username}`} onClick={onItemClick}>
          <User24Icon className="mr-2 h-5 w-5" /> Profile
        </UserMenuItem>
        <UserMenuItem href="/settings/account/profile" onClick={onItemClick}>
          <Settings24Icon className="mr-2 h-5 w-5" /> Settings
        </UserMenuItem>
        <UserMenuItem href="/settings/account/affiliates" onClick={onItemClick}>
          <UserAdd20Icon className="mr-2 h-5 w-5" /> Affiliates
        </UserMenuItem>
      </div>
      <Separator />
      <div className="p-[0.1875rem]">
        <UserMenuItem href="/help-center" onClick={onItemClick}>
          <Help20Icon className="mr-2 h-5 w-5" /> Help center
        </UserMenuItem>
        <UserMenuItem
          onClick={() => {
            onItemClick()
            logout()
          }}
        >
          <Logout20Icon className="mr-2 h-5 w-5" /> Logout
        </UserMenuItem>
      </div>
    </>
  )
}
