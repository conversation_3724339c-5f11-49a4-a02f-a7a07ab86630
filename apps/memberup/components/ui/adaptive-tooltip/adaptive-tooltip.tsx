'use client'

import * as TooltipPrimitive from '@radix-ui/react-tooltip'
import * as React from 'react'

import { cn } from '@/lib/utils'

function AdaptiveTooltipProvider({
  delayDuration = 0,
  ...props
}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {
  return <TooltipPrimitive.Provider data-slot="tooltip-provider" delayDuration={delayDuration} {...props} />
}

/**
 * AdaptiveTooltip - A tooltip component that works on hover, touch and keyboard events
 *
 * This component enhances Radix UI's tooltip with adaptive behavior:
 * - **Mouse**: Traditional hover behavior - shows on hover, hides on mouse leave
 * - **Touch**: Tap to toggle - tap to open, tap again or tap outside to close
 * - **Keyboard**: Press Enter or Space to toggle open/closed
 *
 * Key features:
 * - Trigger methods work independently without blocking each other
 * - Touch can take over from hover (useful when testing responsive designs)
 * - Click-outside detection for touch/keyboard modes only
 * - Window resize closes the tooltip to prevent positioning issues
 *
 * @param children - Must include AdaptiveTooltipTrigger and AdaptiveTooltipContent components
 * @param props - Additional props passed to Radix UI's Tooltip.Root
 *
 * @example
 * ```tsx
 * <AdaptiveTooltip>
 *   <AdaptiveTooltipTrigger asChild>
 *     <button>Hover or tap me</button>
 *   </AdaptiveTooltipTrigger>
 *   <AdaptiveTooltipContent>
 *     <p>Tooltip content</p>
 *   </AdaptiveTooltipContent>
 * </AdaptiveTooltip>
 * ```
 */
function AdaptiveTooltip({ children, ...props }: React.ComponentProps<typeof TooltipPrimitive.Root>) {
  const [open, setOpen] = React.useState(false)
  const [openedBy, setOpenedBy] = React.useState<'hover' | 'touch' | 'keyboard' | null>(null)
  const triggerRef = React.useRef<HTMLButtonElement>(null)
  const contentRef = React.useRef<HTMLDivElement>(null)

  // Close tooltip on window resize
  React.useEffect(() => {
    const handleResize = () => {
      setOpen(false)
      setOpenedBy(null)
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Handle Radix's hover behavior
  const handleOpenChange = (newOpen: boolean) => {
    // If opening via hover
    if (newOpen && !open) {
      setOpen(true)
      setOpenedBy('hover')
    }
    // If closing and it was opened by hover
    else if (!newOpen && openedBy === 'hover') {
      setOpen(false)
      setOpenedBy(null)
    }
    // Let hover close work if no other interaction opened it
    else if (!newOpen && openedBy === null) {
      setOpen(false)
    }
  }

  const handlePointerDown = React.useCallback(
    (e: React.PointerEvent) => {
      if (e.pointerType === 'touch') {
        e.preventDefault()
        e.stopPropagation() // Prevent link navigation on mobile

        if (!open || openedBy === 'hover') {
          // Open on touch or take over from hover
          setOpen(true)
          setOpenedBy('touch')
        } else if (openedBy === 'touch') {
          // Close if it was opened by touch
          setOpen(false)
          setOpenedBy(null)
        }
      }
    },
    [open, openedBy],
  )

  // Handle click events to prevent propagation on mobile
  const handleClick = React.useCallback(
    (e: React.MouseEvent) => {
      if (openedBy === 'touch') {
        e.preventDefault()
        e.stopPropagation()
      }
    },
    [openedBy],
  )

  const handleKeyDown = React.useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault()
        e.stopPropagation()

        if (!open) {
          setOpen(true)
          setOpenedBy('keyboard')
        } else {
          setOpen(false)
          setOpenedBy(null)
        }
      }
    },
    [open],
  )

  // Handle clicks outside for touch/keyboard modes
  React.useEffect(() => {
    if (!open || openedBy === 'hover' || openedBy === null) return

    const handleClickOutside = (e: PointerEvent) => {
      const target = e.target as Node

      // Check if click is on trigger or content
      if (
        triggerRef.current?.contains(target) ||
        contentRef.current?.contains(target) ||
        (target instanceof Element && target.closest('[data-slot="tooltip-content"]'))
      ) {
        return
      }

      // Close tooltip
      setOpen(false)
      setOpenedBy(null)
    }

    // Small delay to prevent immediate closing
    const timer = setTimeout(() => {
      document.addEventListener('pointerdown', handleClickOutside, true)
    }, 100)

    return () => {
      clearTimeout(timer)
      document.removeEventListener('pointerdown', handleClickOutside, true)
    }
  }, [open, openedBy])

  return (
    <TooltipPrimitive.Root data-slot="tooltip" open={open} onOpenChange={handleOpenChange} {...props}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.type === AdaptiveTooltipTrigger) {
          return React.cloneElement(
            child as React.ReactElement<React.ComponentProps<typeof TooltipPrimitive.Trigger>>,
            {
              ref: triggerRef,
              onPointerDown: handlePointerDown,
              onKeyDown: handleKeyDown,
              onClick: handleClick,
            },
          )
        }
        if (React.isValidElement(child) && child.type === AdaptiveTooltipContent) {
          return React.cloneElement(
            child as React.ReactElement<
              React.ComponentProps<typeof TooltipPrimitive.Content> & {
                sideOffset?: number
                className?: string
              }
            >,
            {
              ref: contentRef,
            },
          )
        }
        return child
      })}
    </TooltipPrimitive.Root>
  )
}

const AdaptiveTooltipTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof TooltipPrimitive.Trigger>
>(({ ...props }, ref) => {
  return <TooltipPrimitive.Trigger ref={ref} {...props} />
})
AdaptiveTooltipTrigger.displayName = 'AdaptiveTooltipTrigger'

const AdaptiveTooltipContent = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<typeof TooltipPrimitive.Content> & {
    sideOffset?: number
    className?: string
  }
>(({ className, sideOffset = 4, children, ...props }, ref) => {
  return (
    <TooltipPrimitive.Portal>
      <TooltipPrimitive.Content
        ref={ref}
        data-slot="tooltip-content"
        sideOffset={sideOffset}
        className={cn(
          'adaptive-tooltip-content z-[1500] overflow-hidden rounded-xl border border-grey-200 bg-white-100 px-3 py-[0.4375rem] text-ssm text-black-200 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-grey-900 dark:bg-black-500 dark:text-black-100',
          className,
        )}
        onPointerDown={(e) => e.stopPropagation()}
        {...props}
      >
        {children}
      </TooltipPrimitive.Content>
    </TooltipPrimitive.Portal>
  )
})
AdaptiveTooltipContent.displayName = 'AdaptiveTooltipContent'

export { AdaptiveTooltip, AdaptiveTooltipTrigger, AdaptiveTooltipContent, AdaptiveTooltipProvider }
