import { UserDetailsHoverCard } from '@/components/community/user-details-hover-card'
import { ProfilePicture } from '@/components/images/profile-picture'
import { IUser } from '@/shared-types/interfaces'

export function AvatarList({ users }: { users: IUser[] }) {
  return (
    <div className="flex -space-x-[0.5625rem]">
      {users.map((user) => (
        <UserDetailsHoverCard key={user.username} username={user.username}>
          <div className="isolate cursor-pointer">
            <ProfilePicture
              className="flex h-8 w-8 items-center justify-center overflow-hidden rounded-full"
              src={user.profile.image}
              cropArea={user.profile.image_crop_area}
              alt={user.name}
              height={32}
              width={32}
            />
          </div>
        </UserDetailsHoverCard>
      ))}
    </div>
  )
}
