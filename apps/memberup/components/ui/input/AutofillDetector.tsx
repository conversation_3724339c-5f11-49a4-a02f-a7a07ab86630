'use client'

import { MutableRefObject, ReactNode, useEffect, useRef, useState } from 'react'

export interface AutofillDetectorProps {
  value: string | number | readonly string[] | undefined
  externalRef: ((instance: HTMLInputElement | null) => void) | MutableRefObject<HTMLInputElement | null> | null
  render: (props: {
    active: boolean
    hasAutofilled: boolean
    isFocused: boolean
    setHasAutofilled: (hasAutofilled: boolean) => void
    setIsFocused: (isFocused: boolean) => void
    ref: (node: HTMLInputElement | null) => void
  }) => ReactNode
}

export function AutofillDetector({ value, externalRef, render }: AutofillDetectorProps) {
  const [hasAutofilled, setHasAutofilled] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const internalRef = useRef<HTMLInputElement>(null)
  const active = Boolean(value) || hasAutofilled
  const timeoutsRef = useRef<ReturnType<typeof setTimeout>[]>([])

  useEffect(() => {
    if (hasAutofilled) {
      timeoutsRef.current.forEach(clearTimeout)
      timeoutsRef.current = []
      return
    }

    const input = internalRef.current
    if (!input) return

    const checkAutofill = () => {
      const inputValue = input.value
      const isAutofilled =
        input.matches(':-webkit-autofill') ||
        input.matches(':-webkit-autofill:hover') ||
        input.matches(':-webkit-autofill:focus')

      if (isAutofilled || (inputValue && !value)) {
        setHasAutofilled(true)
        timeoutsRef.current.forEach(clearTimeout)
        timeoutsRef.current = []
      }
    }

    checkAutofill()
    timeoutsRef.current = [
      setTimeout(checkAutofill, 100),
      setTimeout(checkAutofill, 300),
      setTimeout(checkAutofill, 500),
      setTimeout(checkAutofill, 1000),
    ]

    return () => {
      timeoutsRef.current.forEach(clearTimeout)
    }
  }, [internalRef.current, value, hasAutofilled])

  const setRefs = (node: HTMLInputElement | null) => {
    internalRef.current = node

    if (typeof externalRef === 'function') {
      externalRef(node)
    } else if (externalRef) {
      ;(externalRef as MutableRefObject<HTMLInputElement | null>).current = node
    }
  }

  return render({
    active,
    hasAutofilled,
    isFocused,
    setIsFocused,
    setHasAutofilled,
    ref: setRefs,
  })
}
