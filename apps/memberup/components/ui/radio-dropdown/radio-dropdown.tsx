'use client'

import { useState } from 'react'

import { ChevronDown20Icon } from '@/components/icons'
import { Button } from '@/components/ui'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { cn } from '@/lib/utils'

export function RadioDropdown({ className, items, label, value, onValueChange, disabled = false }: any) {
  const [open, setOpen] = useState(false)

  const onRadioGroupChange = (value: string) => {
    setOpen(false)
    onValueChange(value)
  }

  return (
    <Popover open={open} onOpenChange={(value) => setOpen(value)}>
      <PopoverTrigger asChild>
        <Button
          disabled={disabled}
          className={cn(
            'flex h-10 max-w-full justify-between bg-white-500 text-black-600 hover:bg-white-400 dark:bg-black-500 dark:text-black-200',
            className,
          )}
        >
          <div className="min-w-0 flex-1">
            <span className="block min-w-0 flex-1 overflow-hidden truncate text-left">{label}</span>
          </div>
          <ChevronDown20Icon className="ml-2" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full min-w-[var(--radix-popover-trigger-width)] p-4">
        <RadioGroup defaultValue="option-one" value={value} onValueChange={onRadioGroupChange}>
          {items.map((item: any) => (
            <div className="flex items-center" key={item.value}>
              <Label
                className="group flex h-8 w-full cursor-pointer items-center text-black-700 dark:text-white-200"
                htmlFor={`radio-${item.value}`}
              >
                <RadioGroupItem value={item.value} id={`radio-${item.value}`} />
                <span className="ml-2">{item.label}</span>
              </Label>
            </div>
          ))}
        </RadioGroup>
      </PopoverContent>
    </Popover>
  )
}
