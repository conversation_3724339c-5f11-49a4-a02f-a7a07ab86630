'use client'

import * as RadioGroupPrimitive from '@radix-ui/react-radio-group'
import * as React from 'react'

import { RadioActive16Icon, RadioInactive16Icon } from '@/components/icons'
import { cn } from '@/lib/utils'

export interface RadioGroupItemProps extends React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item> {
  variant?: 'primary' | 'flat'
}

const RadioGroup = React.forwardRef<
  React.ElementRef<typeof RadioGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>
>(({ className, ...props }, ref) => {
  return <RadioGroupPrimitive.Root className={cn(className)} {...props} ref={ref} />
})
RadioGroup.displayName = RadioGroupPrimitive.Root.displayName

const RadioGroupItem = React.forwardRef<React.ElementRef<typeof RadioGroupPrimitive.Item>, RadioGroupItemProps>(
  ({ className, variant = 'primary', ...props }, ref) => {
    return (
      <RadioGroupPrimitive.Item
        ref={ref}
        className={cn(
          'relative aspect-square h-4 w-4 rounded-full bg-transparent focus:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
          className,
        )}
        {...props}
      >
        <RadioGroupPrimitive.Indicator className="absolute left-0 top-0 flex h-4 w-4 items-center justify-center">
          <RadioActive16Icon
            className={cn('h-4 w-4', variant === 'primary' ? 'text-primary-200' : 'text-black-700 dark:text-white-500')}
          />
        </RadioGroupPrimitive.Indicator>
        <RadioInactive16Icon className="absolute left-0 top-0 h-4 w-4" />
        <RadioActive16Icon className="hover-icon absolute left-0 top-0 z-50 h-4 w-4 text-black-200 opacity-0 transition-opacity group-hover:opacity-100" />
      </RadioGroupPrimitive.Item>
    )
  },
)
RadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName

export { RadioGroup, RadioGroupItem }
