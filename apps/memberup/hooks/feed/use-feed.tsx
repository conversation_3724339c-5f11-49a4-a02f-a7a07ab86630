import { cloneDeep } from 'lodash'
import { usePathname } from 'next/navigation'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useChatContext } from 'stream-chat-react'

import { FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { transformMessage } from '@/memberup/libs/utils'

const PAGE_SIZE = 10

const useFeed = () => {
  const membership = useStore((state) => state.community.membership)
  const { client: chatClient } = useChatContext()
  const feed = useStore((state) => state.feed)
  const { initializeSpaces, selectedSpaceId } = useStore((state) => state.feed)
  const { connecting: streamChatConnecting, connected: streamChatConnected } = useStore((state) => state.streamChat)
  const user = useStore((state) => state.auth.user)
  const feedInitialized = useRef(false)
  const pathname = usePathname()
  const previousChatUser = useRef<string | undefined>(undefined)

  const [isLoadingMessages, setIsLoadingMessages] = useState(true)
  const [isLoadingMore, setIsLoadingMore] = useState(false)

  // Reset feed initialization when chat user changes or connection state changes
  useEffect(() => {
    const currentUserId = chatClient?.user?.id
    if (previousChatUser.current !== currentUserId || !streamChatConnected) {
      feedInitialized.current = false
      previousChatUser.current = currentUserId
    }
  }, [chatClient?.user?.id, streamChatConnected])

  const loadPinnedMessages = useCallback(async () => {
    if (!chatClient) return

    const filters = {
      deleted_at: { $exists: false },
      parent_id: { $exists: false },
      pinned: { $eq: true },
      feed_status: { $nin: [FEED_STATUS_ENUM.rejected, FEED_STATUS_ENUM.reported] },
    }

    const orderBy = getGetStreamOrderByClause(feed.getSelectedSpaceOrderBy('community'))
    const channelFiltersForPinnedMessages = { id: { $in: membership.channels.map((c: any) => c.id) } }

    const response = await chatClient.search(channelFiltersForPinnedMessages, filters, {
      sort: orderBy,
      limit: PAGE_SIZE,
    })
    const pinnedMessages = response.results.map((r) => cloneDeep(transformMessage(r.message)))
    feed.setPinnedMessages(pinnedMessages)
  }, [chatClient, feed, membership])

  // TODO: For some actions like changing sort, we must load the first page by setting next to null
  const loadMessages = useCallback(
    async (forceFirstPage = false) => {
      // Use different loading states for initial load vs loading more
      if (forceFirstPage) {
        setIsLoadingMessages(true)
      } else {
        setIsLoadingMore(true)
      }

      try {
        // Search for posts
        const selectedSpaceIds =
          feed.selectedSpaceId === 'community' ? Object.keys(feed.spaces) : [feed.selectedSpaceId]
        const channelFilters = { id: { $in: selectedSpaceIds } }
        const messageFilters = {
          deleted_at: { $exists: false },
          parent_id: { $exists: false },
          pinned: { $eq: false },
          feed_status: { $nin: [FEED_STATUS_ENUM.rejected, FEED_STATUS_ENUM.reported] },
        }

        const currentSortBy = feed.getSelectedSpaceOrderBy()
        const orderByClause = getGetStreamOrderByClause(currentSortBy)

        const next = forceFirstPage ? null : feed.getSelectedSpaceNext()

        const searchResponse = await chatClient.search(channelFilters, messageFilters, {
          sort: orderByClause,
          limit: PAGE_SIZE,
          next: next,
        })

        if (forceFirstPage) {
          // Clear existing messages and add new messages if not fetching the next page
          feed.setMessages(
            feed.selectedSpaceId,
            searchResponse.results.map((r) => r.message),
            searchResponse.next,
          )
        } else {
          // Add new messages without clearing when fetching the next page
          feed.addMessages(
            feed.selectedSpaceId,
            searchResponse.results.map((r) => r.message),
            searchResponse.next,
          )
        }
      } catch (error) {
        console.error('Failed to load messages:', error)
      } finally {
        // Reset the appropriate loading state
        if (forceFirstPage) {
          setIsLoadingMessages(false)
        } else {
          setIsLoadingMore(false)
        }
      }
    },
    [chatClient, feed],
  )

  useEffect(() => {
    if (!selectedSpaceId || !membership || !chatClient || streamChatConnecting || !streamChatConnected) return

    // Additional check: ensure Stream client user state matches auth state
    const streamUserId = chatClient.user?.id
    // Anonymous users have IDs starting with '!anon'
    const isAnonymous = streamUserId?.startsWith('!anon') || chatClient.user?.anon === true

    // Valid states:
    // 1. User logged in and Stream has matching user ID
    // 2. User logged out and Stream has anonymous user
    const isStreamUserValid = user ? streamUserId === user.id : isAnonymous

    if (!isStreamUserValid) {
      // Stream client user doesn't match auth state, wait for reconnection
      feedInitialized.current = false // Reset initialization flag
      return
    }

    const fetchInitialMessages = async () => {
      if (chatClient && !feedInitialized.current) {
        feedInitialized.current = true
        setIsLoadingMessages(true)
        try {
          initializeSpaces(membership.channels, selectedSpaceId)
          await loadPinnedMessages()
          await loadMessages(true)
        } catch (error) {
          console.error('Failed to load messages:', error)
        } finally {
          setIsLoadingMessages(false)
        }
      }
    }

    fetchInitialMessages()
  }, [
    membership,
    chatClient,
    selectedSpaceId,
    initializeSpaces,
    loadPinnedMessages,
    loadMessages,
    streamChatConnecting,
    streamChatConnected,
    user,
  ])

  useEffect(() => {
    // Set loading state when changing communities
    if (pathname.split('/')[1] !== membership?.slug) {
      setIsLoadingMessages(true)
    }
  }, [pathname, membership?.slug])

  const getGetStreamOrderByClause = (sortBy: string): any[] => {
    const orderByClause: any[] = []
    if (sortBy === 'newest') {
      orderByClause.push({ created_at: -1 })
    } else if (sortBy === 'activity') {
      orderByClause.push({ last_activity_at: -1 })
    }
    return orderByClause
  }

  const setNewSortBy = async (newSortBy: 'newest' | 'activity') => {
    feed.setSelectedSpaceOrderBy(newSortBy)
    await loadMessages(true)
  }

  const setNewSpaceId = async (spaceId: string) => {
    feed.setSelectedSpaceId(spaceId)
    await loadMessages(true)
  }

  const fetchMoreMessages = async () => {
    await loadMessages()
  }

  const unpinMessage = async (message) => {
    const result = await chatClient.unpinMessage(message.id)
    feed.unpinMessage(result.message)
    toast.success('The post has been unpinned from the Community space.')
  }

  const pinMessage = async (message) => {
    const result = await chatClient.pinMessage(message.id)

    feed.pinMessage(result.message)
    toast.success('The post has been pinned to the Community space.')
  }

  return {
    isLoadingMessages: isLoadingMessages || streamChatConnecting,
    isLoadingMore,
    pinnedMessages: feed.pinnedMessages,
    messages: feed.getSelectedSpaceMessages(), // Messages of the current selected space
    next: feed.getSelectedSpaceNext() || '', // Next pointer of the current selected space
    isCommunitySpace: feed.selectedSpaceId === 'community',
    sortBy: feed.getSelectedSpaceOrderBy(), // Sort by of the current selected space
    fetchMoreMessages: fetchMoreMessages,
    pinMessage: pinMessage,
    unpinMessage: unpinMessage,
    setNewSortBy,
    setNewSpaceId,
    spaceId: feed.selectedSpaceId,
  }
}

export default useFeed
