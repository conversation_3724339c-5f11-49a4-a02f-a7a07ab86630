import { UpChunk } from '@mux/upchunk'
import { useCallback, useRef, useState } from 'react'

import { uploadFileToCloudinaryApi } from '@memberup/shared/src/services/apis/cloudinary.api'
import { LIBRARY_CONTENT_ENUM } from '@memberup/shared/src/types/enum'
import { getFileType } from '@/shared-libs/file'
import { getMuxUploadDataApi } from '@/shared-services/apis/mux.api'

export interface IUploadFile {
  file: File
  url?: string
  id: string
  progress?: number
  type?: LIBRARY_CONTENT_ENUM
  error?: any
}

export type IFileUploadsState = Record<string, IUploadFile>

const useMultipleFileUploads = (prefix: string, membershipId: string) => {
  const [fileUploads, setFileUploads] = useState<IFileUploadsState>({})
  const muxUploadReferences = useRef({})
  const cloudinaryCancelReferences = useRef({})

  const uploadFileToCloudinary = async (file: IUploadFile) => {
    const res = await uploadFileToCloudinaryApi(
      file.file,
      'image',
      (progress: number) => {
        setFileUploads((prevFileUploads: IFileUploadsState) => {
          return {
            ...prevFileUploads,
            [file.id]: {
              ...prevFileUploads[file.id],
              progress: Math.floor(progress * 100),
            },
          }
        })
      },
      (source: any) => {
        cloudinaryCancelReferences.current[file.id] = source.cancel
      },
    )

    if (!res) return null

    delete cloudinaryCancelReferences.current[file.id]

    const { data } = res

    if (data) {
      return {
        id: file.id,
        asset_id: data.asset_id,
        filename: file.file.name,
        url: data.secure_url,
        mimetype: file.file.type,
        size_in_bytes: res?.data.bytes,
        public_id: data.public_id,
        width: data.width,
        height: data.height,
        format: data.format,
        created_at: data.created_at,
      }
    } else {
      return null
    }
  }

  const uploadFileToMux = async (file: IUploadFile) => {
    const passthrough = `${prefix}:${membershipId}:${file.id}`

    const { data: uploadData } = await getMuxUploadDataApi(passthrough, false, membershipId, false)

    return new Promise((resolve, reject) => {
      const upload = UpChunk.createUpload({
        endpoint: uploadData.data.mux_upload_url,
        file: file.file,
        attempts: 3,
        chunkSize: 102400, // Uploads the file in ~100mb chunks
      })

      upload.on('error', () => {
        reject()
      })

      upload.on('progress', (temp) => {
        setFileUploads((prevFileUploads: IFileUploadsState) => {
          return {
            ...prevFileUploads,
            [file.id]: {
              ...prevFileUploads[file.id],
              progress: Math.floor(temp.detail),
            },
          }
        })
      })

      upload.on('success', () => {
        delete muxUploadReferences.current[file.id]

        resolve({
          id: file.id,
          mimetype: file.file.type,
          filename: file.file.name,
          passthrough,
          mux_upload_id: uploadData.data.mux_upload_id,
          public_id: uploadData.public_id,
        })
      })

      muxUploadReferences.current[file.id] = upload
    })
  }

  const uploadFile = (file: IUploadFile) => {
    const fileType = getFileType(file.file)

    if (fileType === LIBRARY_CONTENT_ENUM.image) {
      return uploadFileToCloudinary(file)
    } else if (fileType === LIBRARY_CONTENT_ENUM.video) {
      return uploadFileToMux(file)
    }
  }

  const uploadFiles = useCallback((files: IUploadFile[]) => {
    const uploads = []

    files.forEach((file) => {
      uploads.push(uploadFile(file))
    })

    setFileUploads((prevFileUploads: IFileUploadsState) => {
      return {
        ...prevFileUploads,
        ...files.reduce((acc, file) => {
          acc[file.id] = file
          return acc
        }, {}),
      }
    })

    return Promise.all(uploads)
  }, [])

  const cancelUpload = (fileId: string): void => {
    const muxUpload = muxUploadReferences.current[fileId]
    const cloudinaryCancelFunction = cloudinaryCancelReferences.current[fileId]

    if (muxUpload) {
      muxUpload.abort()
      delete muxUploadReferences.current[fileId]
    }

    if (cloudinaryCancelFunction) {
      cloudinaryCancelFunction()
      delete cloudinaryCancelReferences.current[fileId]
    }

    setFileUploads((prevFileUploads: IFileUploadsState) => {
      return {
        ...prevFileUploads,
        [fileId]: {
          ...prevFileUploads[fileId],
          progress: -1, // Use -1 or another special value to indicate cancellation
          status: 'cancelled',
        },
      }
    })
  }

  const cancel = () => {
    Object.keys(muxUploadReferences.current).forEach((uploadId) => {
      cancelUpload(uploadId)
      delete muxUploadReferences.current[uploadId]
    })

    Object.keys(cloudinaryCancelReferences.current).forEach((uploadId) => {
      cancelUpload(uploadId)
      delete cloudinaryCancelReferences.current[uploadId]
    })
  }

  return {
    fileUploads,
    uploadFiles,
    cancel,
  }
}

export default useMultipleFileUploads
