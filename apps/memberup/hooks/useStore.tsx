import { useContext } from 'react'
import { useStore as zustandUseStore } from 'zustand'

import { StoreContext } from '@/components/providers/StoreProvider'
import { createStore, type StoreState } from '@/store'

export type StoreApi = ReturnType<typeof createStore>

export const useStore = <T,>(selector: (store: StoreState) => T): T => {
  const storeContext = useContext(StoreContext)

  if (!storeContext) {
    throw new Error(`useStore must be used within StoreProvider`)
  }

  return zustandUseStore(storeContext, selector)
}
