// Adapted from https://valentinh.github.io/react-easy-crop/
import { ValidationResult } from '@/lib/validation/types'
import { CropArea } from '@/shared-types/types'

export const createImage: (url: string) => Promise<HTMLImageElement> = (url) =>
  new Promise((resolve, reject) => {
    const image = new Image()
    image.addEventListener('load', () => resolve(image))
    image.addEventListener('error', (error) => reject(error))
    image.setAttribute('crossOrigin', 'anonymous') // needed to avoid cross-origin issues on CodeSandbox
    image.src = url
  })

export function getRadianAngle(degreeValue: number) {
  return (degreeValue * Math.PI) / 180
}

export function rotateSize(width: number, height: number, rotation: number) {
  const rotRad = getRadianAngle(rotation)

  return {
    width: Math.abs(Math.cos(rotRad) * width) + Math.abs(Math.sin(rotRad) * height),
    height: Math.abs(Math.sin(rotRad) * width) + Math.abs(Math.cos(rotRad) * height),
  }
}

export async function getCroppedImg(
  imageSrc: string,
  percentageCropArea: CropArea,
  rotation = 0,
  flip = { horizontal: false, vertical: false },
): Promise<string> {
  const image = await createImage(imageSrc)
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')

  const pixelCropArea = {
    x: (percentageCropArea.x / 100) * image.width,
    y: (percentageCropArea.y / 100) * image.height,
    width: (percentageCropArea.width / 100) * image.width,
    height: (percentageCropArea.height / 100) * image.height,
  }

  if (!ctx) {
    return null
  }

  const rotRad = getRadianAngle(rotation)

  const { width: bBoxWidth, height: bBoxHeight } = rotateSize(image.width, image.height, rotation)

  canvas.width = bBoxWidth
  canvas.height = bBoxHeight

  ctx.translate(bBoxWidth / 2, bBoxHeight / 2)
  if (rotation) {
    ctx.rotate(rotRad)
  }
  ctx.scale(flip.horizontal ? -1 : 1, flip.vertical ? -1 : 1)
  ctx.translate(-image.width / 2, -image.height / 2)

  ctx.drawImage(image, 0, 0)

  const croppedCanvas = document.createElement('canvas')

  const croppedCtx = croppedCanvas.getContext('2d')

  if (!croppedCtx) {
    return null
  }

  croppedCanvas.width = pixelCropArea.width
  croppedCanvas.height = pixelCropArea.height

  croppedCtx.drawImage(
    canvas,
    pixelCropArea.x,
    pixelCropArea.y,
    pixelCropArea.width,
    pixelCropArea.height,
    0,
    0,
    pixelCropArea.width,
    pixelCropArea.height,
  )

  return new Promise((resolve) => {
    croppedCanvas.toBlob((file) => {
      resolve(URL.createObjectURL(file))
    }, 'image/jpeg')
  })
}

export const validateImage = (
  file: File,
  minWidth: number,
  minHeight: number,
  maxSizeMB?: number,
): Promise<ValidationResult> => {
  return new Promise((resolve) => {
    const img = new Image()
    img.src = URL.createObjectURL(file)

    img.onload = () => {
      URL.revokeObjectURL(img.src)

      const fileSizeMB = file.size / (1024 * 1024)

      if (maxSizeMB && fileSizeMB > maxSizeMB) {
        resolve({ valid: false, error: `Image size exceeds maximum of ${maxSizeMB}MB` })
      } else if (img.width < minWidth || img.height < minHeight) {
        resolve({ valid: false, error: `Image dimensions too small. Minimum: ${minWidth}x${minHeight}px` })
      }

      resolve({ valid: true })
    }

    img.onerror = () => {
      URL.revokeObjectURL(img.src)
      resolve({ valid: false, error: 'Failed to load image for validation' })
    }
  })
}
