import { fullscreenLayoutPathnames, nonCommunityPathnames } from './constants'
import { SPACE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { IMembershipSetting } from '@memberup/shared/src/types/interfaces'

export const generateSpacesData = (channels: any) => {
  const spaces = channels
    .filter((c: any) => c.active && c.visibility)
    .map((c: any) => ({
      id: c.id,
      name: c.name,
      slug: c.slug,
      messages: [],
      orderBy: 'activity',
      next: undefined,
      spaceType: c.space_type,
      channelType: c.type,
    }))
    .reduce((acc: any, obj: any) => {
      acc[obj.id] = obj
      return acc
    }, {})

  spaces['community'] = {
    id: 'community',
    name: 'Community',
    slug: 'community',
    messages: [],
    pinnedMessages: [],
    orderBy: 'activity',
    next: undefined,
    spaceType: SPACE_TYPE_ENUM.home,
  }

  return spaces
}

export const isCommunityPath = (pathname: string) => {
  return !nonCommunityPathnames.some((nonCommunityPathname) => {
    return pathname.startsWith(nonCommunityPathname)
  })
}

export const isPaidCommunity = (membershipSetting: IMembershipSetting) => {
  return membershipSetting?.stripe_connect_account?.enabled !== false && membershipSetting?.is_pricing_enabled
}

export function testForCommunityPath(communityPath: string, pathname: string) {
  // Escape special regex characters in the pattern
  const escaped = communityPath.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  // Build the regex pattern that allows for the initial path component
  return new RegExp(`^/[^/]+${escaped}.*$`).test(pathname)
}

export function testForFullscreenCommunityLayoutPath(pathname: string) {
  return fullscreenLayoutPathnames.some((communityPath) => testForCommunityPath(communityPath, pathname))
}
