import { intervalToDuration } from 'date-fns'

export function formatDistanceToNowMinimal(date: Date) {
  const duration = intervalToDuration({ start: date, end: new Date() })

  if (duration.years) return `${duration.years}y`
  if (duration.months) return `${duration.months}mo`
  if (duration.weeks) return `${duration.weeks}w`
  if (duration.days) return `${duration.days}d`
  if (duration.hours) return `${duration.hours}h`
  if (duration.minutes) return `${duration.minutes}min`

  return `${duration.seconds}s`
}
