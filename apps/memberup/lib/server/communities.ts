import * as Sentry from '@sentry/nextjs'

import { inngest } from '@memberup/shared/src/services/inngest'
import { membershipQueryOptions } from '@/lib/query-options/communities'
import prisma from '@/shared-libs/prisma/prisma'
import { IMembership } from '@/shared-types/interfaces'

export const getCommunityDataBySlug: (slug: string) => Promise<IMembership | null> = async (slug: string) => {
  return (await prisma.membership.findUnique({
    where: { slug: slug },
    ...membershipQueryOptions,
  })) as IMembership
}

export const getCachedCommunityData = getCommunityDataBySlug

export const updateMembersCountAsync = async (membershipId: string) => {
  try {
    await inngest.send({
      name: 'membership/update-members-count',
      data: { membershipId },
    })
  } catch (err) {
    console.error('Failed to send Inngest event', err)
    Sentry.captureException(err)
  }
}

export const updateMembersCount = async (membershipId: string): Promise<void> => {
  await prisma.$transaction(async (tx) => {
    const count = await tx.userMembership.count({
      where: {
        membership_id: membershipId,
        status: 'accepted',
      },
    })
    await tx.membershipSetting.update({
      where: { membership_id: membershipId },
      data: { members_count: count },
    })
  })
}
