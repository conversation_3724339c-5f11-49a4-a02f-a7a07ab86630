import DOMPurify from 'dompurify'
import { J<PERSON><PERSON> } from 'jsdom'

export function sanitizeHTML(html: string, allowedTags?: string[]) {
  const window = new JSDOM('').window
  const purify = DOMPurify(window)
  const options: any = {}

  if (allowedTags) {
    options.ALLOWED_TAGS = allowedTags
  }

  return purify.sanitize(html, options)
}

export async function extractOpenGraphFromHTML(html: string, maxLength: number = 160) {
  if (!html || html.trim() === '') {
    return ''
  }

  const dom = new JSDOM(`<div>${html}</div>`)
  const container = dom.window.document.querySelector('div')

  let textContent = container?.textContent || ''

  textContent = textContent.replace(/\s+/g, ' ').trim()

  if (textContent.length <= maxLength) {
    return textContent
  }

  let truncated = textContent.substring(0, maxLength)
  const lastSpaceIndex = truncated.lastIndexOf(' ')

  if (lastSpaceIndex > 0) {
    truncated = truncated.substring(0, lastSpaceIndex)
  }

  return truncated + '...'
}
