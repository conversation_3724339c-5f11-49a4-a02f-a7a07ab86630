import { Knock } from '@knocklabs/node'

import { getCurrentDateDayDateObject } from '@memberup/shared/src/libs/date-utils'
import { prisma } from '@memberup/shared/src/libs/prisma/prisma'
import { createStreamChatUserToken } from '@memberup/shared/src/libs/stream-chat'
import { ACTION_NAME_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { convertDatesToISOStrings } from '@/lib/formatting'
import { fetchUser } from '@/lib/prisma/user'
import { userPublicQueryOptions } from '@/lib/query-options/users'
import { createActionHistory } from '@/shared-libs/prisma/actions'
import { IAuthenticatedUserData, IUser } from '@/shared-types/interfaces'

const KNOCK_SIGNING_KEY = process.env.KNOCK_SIGNING_KEY

export async function getAuthenticatedUserData(id: string): Promise<IAuthenticatedUserData | null> {
  const user = await fetchUser(id)

  if (!user) {
    return null
  }

  // TODO: We must get the membership settings from the list of communities.
  const { profile, ...rest } = user
  let stripeSubscription, streamChatUserToken, knockToken

  const userId = user.id
  // const stripeAccessToken = user.membership_setting?.stripe_connect_account?.access_token
  // const stripeUserId = user.membership_setting?.stripe_connect_account?.stripe_user_id

  // if (
  //   profile.stripe_subscription_id &&
  //   profile.stripe_subscription_id !== 'lifetime' &&
  //   stripeAccessToken
  // ) {
  //   if (![USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(user.role as any)) {
  //     stripeSubscription = await stripeGetSubscription(
  //       stripeAccessToken,
  //       profile.stripe_subscription_id,
  //       stripeUserId
  //     )
  //   }
  // }

  if (user.status === USER_STATUS_ENUM.active || user.status === USER_STATUS_ENUM.unverified) {
    streamChatUserToken = await createStreamChatUserToken(userId)

    if (KNOCK_SIGNING_KEY) {
      knockToken = Knock.signUserToken(userId, { signingKey: KNOCK_SIGNING_KEY })
    }
  }

  rest.email = rest.email.toLocaleLowerCase()

  const day = getCurrentDateDayDateObject()

  const action = await prisma.action.findFirst({
    where: {
      action_name: ACTION_NAME_ENUM.USER_ACTIVE,
    },
  })

  const actionHistory = await prisma.actionHistory.findFirst({
    where: {
      user_id: user.id,
      action_id: action.id,
      resource_id: null,
      createdAt: {
        gte: day,
        lte: new Date(day.getTime() + 24 * 60 * 60 * 1000),
      },
    },
  })

  if (!actionHistory) {
    try {
      await createActionHistory(user.id, action, null)
    } catch (e) {
      console.error(e)
    }
  }

  return {
    profile: convertDatesToISOStrings({
      ...profile,
      stripe_subscription: stripeSubscription || null,
    }),
    user: convertDatesToISOStrings({ ...rest }),
    streamChatUserToken,
    knockToken,
    // time_zone: updatedMembershipSetting?.time_zone,
  } as IAuthenticatedUserData
}

export async function getUserPublicDataByUsername(username: string) {
  return (await prisma.user.findUnique({
    where: {
      status: USER_STATUS_ENUM.active,
      username: username as string,
    },
    ...userPublicQueryOptions,
  })) as IUser | null
}

export function generateUserLastActivityUpdateData(profile: {
  last_activity_at: Date
  last_session_initialized_at?: Date | null
}) {
  const updateData = {
    last_activity_at: new Date(),
  }

  // Only update last_session_initialized_at if the last_activity_at is older than 10 minutes ago
  if (
    new Date(profile.last_activity_at).getTime() < new Date().getTime() - 10 * 60 * 1000 ||
    !profile.last_session_initialized_at
  ) {
    updateData['last_session_initialized_at'] = updateData.last_activity_at
  }

  return updateData
}

export async function updateUserLastActivity(userId: string) {
  const profile = await prisma.userProfile.findUnique({
    where: {
      user_id: userId,
    },
    select: {
      id: true,
      last_activity_at: true,
      last_session_initialized_at: true,
    },
  })

  const updateData = generateUserLastActivityUpdateData(profile)

  return await prisma.userProfile.update({
    where: {
      user_id: userId,
    },
    data: updateData,
  })
}
