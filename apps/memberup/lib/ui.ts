export function scrollIntoViewWithOffset(element: HTMLElement, offset: number) {
  const elementRect = element.getBoundingClientRect()

  const scrollTop = document.documentElement.scrollTop
  const targetPosition = scrollTop + elementRect.top - offset

  document.querySelector('#app-scroll-area > div[data-radix-scroll-area-viewport]').scrollTo({
    top: targetPosition,
    behavior: 'smooth',
  })
}
