import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import prisma from '@/shared-libs/prisma/prisma'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { membership_id: membershipId } = req.body

    let userMembership = await prisma.userMembership.findFirst({
      where: {
        user_id: user.id,
        membership_id: membershipId,
        status: USER_MEMBERSHIP_STATUS_ENUM.pending,
      },
    })

    if (!userMembership) {
      return res.status(400).json({
        success: false,
        message: 'Membership request not found',
      })
    }

    const updatedUserMembership = await prisma.userMembership.update({
      where: {
        id: userMembership.id,
      },
      data: {
        status: USER_MEMBERSHIP_STATUS_ENUM.cancelled,
      },
      include: {
        membership: true,
      },
    })
    return res.json({ success: true, data: updatedUserMembership })
  } catch (e) {
    console.error(e)
    return res.status(500).json({
      success: false,
      message: e.message,
    })
  }
})

export default handler
