import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import prisma from '@/shared-libs/prisma/prisma'
import { setupUserOnExternalServices } from '@/shared-libs/user'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'
import { checkIsPaidMembership } from '@/src/libs/utils'
import authenticationMiddleware from '@/src/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  const { id } = req.query

  const userMembership = await prisma.userMembership.findUnique({
    where: {
      id: id as string,
    },
  })

  if (!userMembership) {
    return res.status(400).json({
      success: false,
      message: 'User Membership not found',
    })
  }

  const updatedUserMembership = await prisma.userMembership.update({
    where: {
      id: userMembership.id,
    },
    data: {
      status: USER_MEMBERSHIP_STATUS_ENUM.rejected,
    },
  })
  return res.json({ success: true, data: updatedUserMembership })
})
export default handler
