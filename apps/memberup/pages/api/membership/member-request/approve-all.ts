import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import prisma from '@/shared-libs/prisma/prisma'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'
import authenticationMiddleware from '@/src/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  const { membership_id: membershipId } = req.query
  const user = req['user']

  const userMembership = await prisma.userMembership.findFirst({
    where: {
      membership_id: membershipId as string,
      user_id: user.id,
      user_role: {
        in: ['owner', 'admin'],
      },
    },
  })
  if (!userMembership) {
    return res.status(403).json({
      success: false,
      message: 'Not enough privileges to manage the Membership',
    })
  }

  const result = await prisma.userMembership.updateMany({
    where: {
      membership_id: membershipId as string,
      status: USER_MEMBERSHIP_STATUS_ENUM.pending,
    },
    data: {
      status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
    },
  })
  return res.json({ success: true, data: result })
})
export default handler
