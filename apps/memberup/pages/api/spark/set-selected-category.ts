import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { findSparkMembershipCategory } from '@memberup/shared/src/libs/prisma/spark-m-category'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import prisma from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const sparkMembershipCategoryId = req.body['id']
      const membershipId = req.query.membership_id as string
      if (!membershipId) {
        return res.status(400).json({ message: `membership_id was not provided.` })
      }

      const membership = await prisma.membership.findFirst({
        where: {
          id: membershipId,
        },
        include: {
          membership_setting: true,
        },
      })

      const category = await prisma.sparkMembershipCategory.findFirst({
        where: {
          id: sparkMembershipCategoryId,
          membership_id: membershipId,
        },
      })
      if (!category) {
        return res.status(400).json({ message: `spark_membership_category_id is not valid.` })
      }

      console.log(membership, category)

      const updatedMembershipSetting = await updateMembershipSetting({
        where: { id: membership.membership_setting.id },
        data: {
          spark_current_membership_category_id: category.id,
        },
      })
      return res.status(200).send({ success: true, data: updatedMembershipSetting })
    } catch (err: any) {
      console.log('err =======', err)
      sentryCaptureException(err)
      return res.status(400).json(errorHandler(err, 'SparkMembershipCategory'))
    }
  })

export default handler
