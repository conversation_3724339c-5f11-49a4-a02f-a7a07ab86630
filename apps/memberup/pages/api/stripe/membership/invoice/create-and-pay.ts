import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import { STRIPE_APPLICATION_FEE_ENTERPRISE, STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { findUserMembershipByIds } from '@/shared-libs/prisma/user-membership'
import { getStripe, stripeGetPrices, stripeUpdateCustomer } from '@/shared-libs/stripe'
import { TStripeConnectAccount } from '@/shared-types/types'

const handler = nc<NextApiRequest, NextApiResponse>({})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { payment_method, selected_price_id } = req.body
    const { membership_id: membershipId } = req.query

    const userMembership = await findUserMembershipByIds(membershipId, user.id)
    if (!userMembership) {
      return status400(res, `You must first start joining procedure.`)
    }

    const membership = userMembership.membership
    const membershipSetting = membership.membership_setting
    const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account as TStripeConnectAccount
    const stripeProductId = membershipSetting?.stripe_product_id

    const stripeCustomerId = userMembership?.stripe_customer_id

    // Find the one time payment price
    const existingStripePricesResult = await stripeGetPrices(connectedStripeAccountInfo, {
      active: true,
      product: stripeProductId,
    })

    const existingStripePrices = existingStripePricesResult.data
    const stripePrice = existingStripePrices.find((p) => p.id === selected_price_id)
    if (!stripePrice) {
      return status400(res, `Price not found`)
    }

    const stripe = getStripe()
    const options = { stripeAccount: connectedStripeAccountInfo.stripe_user_id }

    await stripe.customers.update(
      stripeCustomerId,
      {
        invoice_settings: {
          default_payment_method: payment_method,
        },
      },
      options,
    )

    const applicationFeePercent = STRIPE_APPLICATION_FEE_ENTERPRISE / 100

    await stripe.invoiceItems.create(
      {
        customer: stripeCustomerId,
        price: stripePrice.id,
        quantity: 1,
        currency: stripePrice.currency,
        description: `One time payment access to ${membership.name}`,
        metadata: {
          membership_id: membership.id,
        },
      },
      options,
    )

    const invoice = await stripe.invoices.create(
      {
        auto_advance: true,
        customer: stripeCustomerId,
        collection_method: 'charge_automatically',
        pending_invoice_items_behavior: 'include',
        metadata: {
          membership_id: membership.id,
        },
        application_fee_amount: applicationFeePercent * stripePrice['unit_amount'],
      },
      options,
    )

    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id, { auto_advance: true }, options)

    await stripe.paymentIntents.update(
      finalizedInvoice.payment_intent,
      {
        metadata: {
          membership_id: membership.id,
        },
      },
      options,
    )

    const paidInvoiceResponse = await stripe.invoices.pay(finalizedInvoice.id, {}, options)

    return status200(res, paidInvoiceResponse.data)
  } catch (err: any) {
    console.error(err)
    return status500(res, err.message)
  }
})

export default handler
