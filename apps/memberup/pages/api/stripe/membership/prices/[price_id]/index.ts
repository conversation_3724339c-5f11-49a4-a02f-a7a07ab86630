import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status403 } from '@/shared-libs/api-utils'
import { findUserMembershipAsAdminOrCreator } from '@/shared-libs/prisma/user-membership'
import { stripeDeletePrice } from '@/shared-libs/stripe'
import { IMembership } from '@/shared-types/interfaces'

const handler = nc<NextApiRequest, NextApiResponse>({})

handler.use(authenticationMiddleware).delete(async (req, res) => {
  try {
    const user = req['user']
    const { membership_id } = req.query
    const { price_id } = req.query

    if (!price_id) {
      return status400(res, 'Price ID is required.')
    }

    const membershipId = membership_id as string
    const userMembership = await findUserMembershipAsAdminOrCreator(user.id, membershipId)
    if (!userMembership) {
      return status403(res, `You don't have permissions to delete a Stripe price.`)
    }
    const membership = userMembership.membership as IMembership
    const stripeConnectedAccount = membership.membership_setting?.stripe_connect_account
    if (!stripeConnectedAccount) {
      return status400(res, 'Stripe is not connected for this account.')
    }

    // Delete the price in Stripe
    const deletedPrice = await stripeDeletePrice(stripeConnectedAccount, price_id as string)
    return status200(res, deletedPrice)
  } catch (err: any) {
    console.error('Failed to delete Stripe price ====', err)
    return status400(res, err.message)
  }
})

export default handler
