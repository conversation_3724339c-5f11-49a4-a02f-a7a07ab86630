import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status403, status500 } from '@/shared-libs/api-utils'
import { updateMembershipSetting, updateMembershipSettings } from '@/shared-libs/prisma/membership-settings'
import { findUserMembershipAsAdminOrCreator } from '@/shared-libs/prisma/user-membership'
import { stripeGetPrices, stripeSetProductDefaultPrice } from '@/shared-libs/stripe' // Ensure this function is implemented

import { PRICE_ID_FREE_COMMUNITY } from '@/shared-types/consts'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { membership_id, product_id } = req.body // Get membership ID and price ID from the request body
    const membershipId = membership_id as string
    const { price_id } = req.query

    if (!price_id) {
      return status400(res, 'Price ID is required.')
    }

    // Verify user membership and permissions
    const userMembership = await findUserMembershipAsAdminOrCreator(user.id, membershipId)
    if (!userMembership) {
      return status403(res, `You don't have permissions to set the default price for this community.`)
    }

    // Free community will not have pricing enabled.
    const membership = userMembership.membership
    if (price_id === PRICE_ID_FREE_COMMUNITY) {
      const updatedMembershipSettings = await updateMembershipSetting({
        where: {
          id: membership.membership_setting.id,
        },
        data: {
          is_pricing_enabled: false,
        },
      })
      return status200(res, { default_price: PRICE_ID_FREE_COMMUNITY, membership_setting: updatedMembershipSettings })
    }

    const stripeConnectedAccount = membership.membership_setting?.stripe_connect_account
    if (!stripeConnectedAccount) {
      return status400(res, 'Stripe is not connected for this account.')
    }

    const result = await stripeGetPrices(stripeConnectedAccount, {})
    const stripePrice = result.data.find((p) => p.id === price_id)
    if (!stripePrice) {
      return status400(res, `Stripe price not found.`)
    }

    // Call the function to set the price as default
    await stripeSetProductDefaultPrice(stripeConnectedAccount, stripePrice.product.id, price_id)
    const updatedPricesResult = await stripeGetPrices(stripeConnectedAccount, {})
    const updatedMembershipSettings = await updateMembershipSetting({
      where: {
        id: membership.membership_setting.id,
      },
      data: {
        stripe_prices: updatedPricesResult.data,
        is_pricing_enabled: true,
      },
    })

    return status200(res, {
      default_price: price_id,
      membership_setting: updatedMembershipSettings,
    })
  } catch (err: any) {
    console.error('Error setting price as default:', err)
    return status500(res, err.message)
  }
})

export default handler
