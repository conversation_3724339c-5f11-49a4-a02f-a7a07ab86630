import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status201, status400, status403, status500 } from '@/shared-libs/api-utils'
import { prisma } from '@/shared-libs/prisma/prisma'
import { findUserMembershipAsAdminOrCreator } from '@/shared-libs/prisma/user-membership'
import { stripeCreatePrice, stripeCreateProduct, stripeGetPrices, stripeGetProducts } from '@/shared-libs/stripe'
import { getStripePricesApi } from '@/shared-services/apis/stripe.api'
import { TStripeConnectAccount } from '@/shared-types/types'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { membership_id } = req.query
      const membershipId = membership_id as string
      const { id, name, ...rest } = req.body

      const userMembership = await findUserMembershipAsAdminOrCreator(user.id, membershipId)
      if (!userMembership) {
        return res.status(403).json({ message: `You don't have permissions to get community prices.` })
      }

      const membership = userMembership.membership
      const stripeConnectedAccount = membership.membership_setting?.stripe_connect_account
      let stripeProductId = membership.membership_setting?.stripe_product_id

      if (!stripeConnectedAccount) {
        return res.status(400).json({ success: false, message: 'Stripe is not connected for this account.' })
      }

      if (!stripeProductId) {
        const stripeProducts = await stripeGetProducts(stripeConnectedAccount)
        let memberupProduct = stripeProducts.data.find((p) => p.metadata?.membership_id === membership.id)
        if (!memberupProduct) {
          memberupProduct = await stripeCreateProduct(stripeConnectedAccount, {
            name: `MemberUp ${membership.name}`,
            description: 'MemberUp community product',
            metadata: {
              membership_id: membership.id,
              membership_slug: membership.slug,
            },
          })
        }

        stripeProductId = memberupProduct.id

        await updateMembershipSetting({
          where: { id: membership.membership_setting.id },
          data: { stripe_product_id: memberupProduct.id },
        })
      }

      const existingStripePrices = await stripeGetPrices(stripeConnectedAccount, {
        active: true,
        product: stripeProductId,
      })

      res.status(200).send({ success: true, data: existingStripePrices.data })
    } catch (err: any) {
      res.status(500).json({ message: err.message })
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { membership_id } = req.query
      const { price, is_default, payment_type, title } = req.body

      const membershipId = membership_id as string

      const userMembership = await findUserMembershipAsAdminOrCreator(user.id, membershipId)
      if (!userMembership) {
        return status403(res, `You don't have permissions to add a Stripe price.`)
      }

      const membership = userMembership.membership
      const stripeConnectedAccount = membership.membership_setting?.stripe_connect_account
      const stripeProductId = membership.membership_setting?.stripe_product_id

      if (!stripeConnectedAccount) {
        return status400(res, 'Stripe is not connected for this account.')
      }

      if (!stripeProductId) {
        return status400(res, 'No Stripe product is configured for this account.')
      }

      const parsedAmount = parseFloat(price)
      if (isNaN(parsedAmount)) {
        return res.status(400).json({ success: false, message: 'Invalid price.' })
      }
      const formattedPrice = Math.round(parsedAmount * 100)
      const stripePrice = await stripeCreatePrice(stripeConnectedAccount, {
        unit_amount: formattedPrice,
        currency: 'usd', // Replace with the appropriate currency
        recurring: payment_type === 'one-time' ? undefined : { interval: payment_type },
        product: stripeProductId,
        metadata: { is_default: is_default ? 'true' : 'false', title: title },
      })

      const stripe = new Stripe(STRIPE_SECRET_KEY, {
        apiVersion: '2023-10-16',
        maxNetworkRetries: 2,
      })
      const options = { stripeAccount: stripeConnectedAccount.stripe_user_id }
      if (is_default) {
        await stripe.products.update(stripeProductId, { default_price: stripePrice.id }, options)
      }

      const stripePricesResult = await stripe.prices.list({ expand: ['data.product.default_price'] }, options)
      await updateMembershipSetting({
        where: { id: membership.membership_setting.id },
        data: {
          stripe_prices: stripePricesResult.data,
        } as any,
      })

      return status201(res, stripePrice)
    } catch (err: any) {
      console.error('Failed to add Stripe price ====', err)
      status500(res, err.message)
    }
  })

export default handler
