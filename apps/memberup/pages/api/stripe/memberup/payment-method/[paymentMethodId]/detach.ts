import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { STRIPE_SECRET_KEY } from '@/shared-config/envs'
import { status200, status500 } from '@/shared-libs/api-utils'
import { stripeDetachPaymentMethod, stripeDetachPaymentMethodMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const paymentMethodId = req.query.paymentMethodId as string

    const result = await stripeDetachPaymentMethodMain(STRIPE_SECRET_KEY, paymentMethodId)
    return status200(res, result)
  } catch (err: any) {
    console.error(err)
    return status500(err.message)
  }
})

export default handler
