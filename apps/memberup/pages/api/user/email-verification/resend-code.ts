import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import { v4 as uuidv4 } from 'uuid'

import { comparePassword } from '@memberup/shared/src/libs/bcrypt'
import { knockBulkDeleteUsers, knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUserById, findUsers, updateUser } from '@memberup/shared/src/libs/prisma/user'
import { getRandomStr } from '@memberup/shared/src/libs/string-utils'
import { KNOCK_WORKFLOW_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import prisma from '@/shared-libs/prisma/prisma'
import { generateEmailVerificationCode, sendEmailVerificationCode } from '@/shared-libs/user'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']

    const userWithCode = await prisma.user.findUnique({
      where: {
        id: user.id,
      },
      select: {
        verification_code: true,
      },
    })

    await sendEmailVerificationCode(user, userWithCode.verification_code)

    res.json({
      success: true,
    })
  } catch (err) {
    console.error(err)
    if (err.response) {
      console.error(err.response.body)
    }
    res.status(500).json(errorHandler(err, 'Email verification code resend failed. Please try again.'))
  }
})

export default handler
