import { Knock } from '@knocklabs/node'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findUserById, updateUser } from '@memberup/shared/src/libs/prisma/user'
import { createStreamChatUserToken } from '@memberup/shared/src/libs/stream-chat'
import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

const KNOCK_SIGNING_KEY = process.env.KNOCK_SIGNING_KEY
handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { code } = req.body
    const dbUser = await findUserById({
      where: {
        id: user.id,
        status: USER_STATUS_ENUM.unverified,
        verification_code: code,
      },
    })

    if (!dbUser) {
      return res.status(400).json({
        message: 'Verification failed',
      })
    } else {
      await updateUser({
        where: { id: user.id },
        data: {
          status: USER_STATUS_ENUM.active,
          verification_code: null,
        },
      })

      const streamChatUserToken = await createStreamChatUserToken(user.id)
      let knockToken
      if (KNOCK_SIGNING_KEY) {
        knockToken = Knock.signUserToken(user.id, { signingKey: KNOCK_SIGNING_KEY })
      }

      return res.json({
        streamChatUserToken,
        knockToken,
      })
    }
  } catch (err) {
    console.error(err)
    if (err.response) {
      console.error(err.response.body)
    }
    return res.status(500).json(errorHandler(err, 'Email verification failed. Please try again.'))
  }
})

export default handler
