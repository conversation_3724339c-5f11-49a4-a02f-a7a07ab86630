import { PAYMENT_HISTORY_STATUS_ENUM, PAYMENT_HISTORY_TYPE_ENUM, PrismaClient } from '@prisma/client'

import { createPaymentHistory, formatPaymentDescription } from '@/shared-libs/prisma/payment-history'

const prisma = new PrismaClient()

async function testPaymentHistory() {
  try {
    console.log('Testing PaymentHistory creation...')

    // Get a test user
    const testUser = await prisma.user.findFirst({
      where: {
        email: {
          not: '',
        },
      },
      include: {
        membership: true,
      },
    })

    if (!testUser) {
      console.error('No test user found')
      return
    }

    console.log(`Using test user: ${testUser.first_name} ${testUser.last_name} (${testUser.id})`)

    // Create a test payment history record for MemberUp
    const memberupPayment = await createPaymentHistory({
      data: {
        amount: 99.99,
        currency: 'usd',
        description: formatPaymentDescription(PAYMENT_HISTORY_TYPE_ENUM.memberup),
        payment_date: new Date(),
        status: PAYMENT_HISTORY_STATUS_ENUM.paid,
        payment_type: PAYMENT_HISTORY_TYPE_ENUM.memberup,
        stripe_payment_intent_id: 'test_pi_' + Date.now(),
        stripe_charge_id: 'test_ch_' + Date.now(),
        stripe_receipt_url: 'https://example.com/receipt',
        stripe_customer_id: 'test_cus_' + Date.now(),
        user_id: testUser.id,
      },
    })

    console.log('Created MemberUp payment history record:', memberupPayment)

    // Create a test payment history record for membership if user has a membership
    if (testUser.membership) {
      const membershipPayment = await createPaymentHistory({
        data: {
          amount: 49.99,
          currency: 'usd',
          description: formatPaymentDescription(PAYMENT_HISTORY_TYPE_ENUM.membership, testUser.membership.name),
          payment_date: new Date(),
          status: PAYMENT_HISTORY_STATUS_ENUM.paid,
          payment_type: PAYMENT_HISTORY_TYPE_ENUM.membership,
          stripe_payment_intent_id: 'test_pi_' + Date.now(),
          stripe_charge_id: 'test_ch_' + Date.now(),
          stripe_receipt_url: 'https://example.com/receipt',
          stripe_customer_id: 'test_cus_' + Date.now(),
          user_id: testUser.id,
          membership_id: testUser.membership.id,
        },
      })

      console.log('Created membership payment history record:', membershipPayment)
    }

    console.log('Test completed successfully')
  } catch (error) {
    console.error('Error testing payment history:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testPaymentHistory()
