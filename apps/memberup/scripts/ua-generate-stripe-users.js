require('dotenv').config()

const Stripe = require('stripe')
const { PrismaClient } = require('@prisma/client')
const { StreamChat } = require('stream-chat')
const _uniq = require('lodash/uniq')
const { askQuestion, getStreamApps } = require('./common')
const prisma = new PrismaClient()

const DATABASE_URL = process.env.DATABASE_URL
const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY
const STRIPE_SECRET_KEY_TEST = process.env.STRIPE_SECRET_KEY_TEST
const NEXT_PUBLIC_STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE

const stripe = new Stripe(NEXT_PUBLIC_STRIPE_LIVE_MODE === 'true' ? STRIPE_SECRET_KEY : STRIPE_SECRET_KEY_TEST, {
  apiVersion: '2023-10-16',
  maxNetworkRetries: 2,
})

const getFullName = (firstName, lastName, defaultValue, maxLength) => {
  // No names provided
  if (!firstName && !lastName) return defaultValue

  let fullName = firstName && lastName ? `${firstName} ${lastName}` : `${firstName}` || `${lastName}`

  if (maxLength) {
    fullName = fullName.length > maxLength ? fullName.substring(0, maxLength - 2) + ' ...' : fullName
  }
  return fullName
}

const delay = (ms) => new Promise((res) => setTimeout(res, ms))

const main = async () => {
  try {
    console.log(`Using Database URL ${DATABASE_URL}`)
    console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)
    const answer = await askQuestion('Do you want to proceed? (y/n): ')
    if (answer.toLowerCase() !== 'y') {
      console.log('Good bye.')
      return
    }

    const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
      timeout: 30000,
    })

    // TODO: Get the membership settings stripe_customer and assign to the owners.
    /*
        update user_profiles up
join users u on up.user_id = u.id
join membership_settings ms on u.membership_id = ms.membership_id
set up.stripe_customer_id = ms.stripe_customer_id WHERE
up.stripe_customer_id is null and u.role = 'owner'
         */

    const users = await prisma.user.findMany({
      where: {
        status: 'active',
        profile: {
          stripe_customer_id: null,
        },
      },
    })

    console.log(`Going to update ${users.length} users.`)
    for (let user of users) {
      console.log(`Processing ${user.id}`)
      const payload = {
        email: user.email,
        description: '',
        name: getFullName(user.first_name, user.last_name, ''),
        metadata: {
          user_id: user.id,
        },
      }

      const newStripeCustomer = await stripe.customers.create(payload)
      const result = await prisma.userProfile.update({
        where: { user_id: user.id },
        data: { stripe_customer_id: newStripeCustomer.id },
      })
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

main()
