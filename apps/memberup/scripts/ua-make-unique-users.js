require('dotenv').config()

const { PrismaClient, Prisma } = require('@prisma/client')
const prisma = new PrismaClient()

// NOTE: We don't generate usernames for invited users as they will create their username after completing the signup process.

function generateUsername(firstName, lastName) {
  // Create an array with the non-empty first and last names
  let parts = [firstName, lastName].filter((name) => name !== '' && name !== null)
  // Join the parts with a dash if both are present
  let baseUsername = parts.join('-')
  // Generate a random 5-character alphanumeric string
  let randomString = Math.random().toString(36).substring(2, 7)
  // Create the final username
  let username = baseUsername ? `${baseUsername}-${randomString}` : randomString
  return username.toLowerCase()
}

const main = async () => {
  try {
    // Get the users that are unique and mark as primary
    const markUniqueUsersQuery = `update users set is_primary = true where email not in (select email from (select count(*) as c, email from users u where u.status = 'active' group by email) as t where t.c > 1)`
    await prisma.$queryRaw(Prisma.raw(markUniqueUsersQuery))

    // Mark the most active user from the duplicates as primary
    const markDuplicatedUsersQuery = `update users set is_primary = true where id in (SELECT 
  id
FROM (
  SELECT 
    u.id, 
    u.email, 
    up.last_activity_at, 
    u.role, 
    up.login_count, 
    u.status,
    ROW_NUMBER() OVER (PARTITION BY u.email ORDER BY u.email ASC, up.login_count DESC, up.last_activity_at DESC, u.role DESC) as rn
  FROM 
    users u
  INNER JOIN 
    user_profiles up ON up.user_id = u.id
  WHERE 
    u.email IN (
      SELECT 
        email 
      FROM 
        (SELECT count(*) as c, email 
         FROM users u 
         WHERE u.status = 'active' 
         GROUP BY email) as t 
      WHERE t.c > 1
    )
    AND u.status = 'active' 
) AS subquery
WHERE rn = 1)`

    await prisma.$queryRaw(Prisma.raw(markDuplicatedUsersQuery))

    // Get all the secondary emails and set the id into related_users of the primary
    const query = `select GROUP_CONCAT(id) as ids, email, count(*) from users where is_primary = false and status = 'active' group by email`
    const secondaryUsers = await prisma.$queryRaw(Prisma.raw(query))
    console.log(`Going to process ${secondaryUsers.length} users marking as a related_user`)
    // NOTE: We run update many but we are going to actually always update just one record.
    const updates = secondaryUsers.map((user) => {
      return prisma.user.updateMany({
        where: {
          email: user.email,
          is_primary: true,
        },
        data: {
          related_users: user.ids.split(','),
        },
      })
    })
    // Execute all updates concurrently
    await Promise.all(updates)
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

main()
