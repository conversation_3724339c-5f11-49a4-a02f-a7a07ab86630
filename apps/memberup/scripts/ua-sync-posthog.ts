import { PrismaClient } from '@prisma/client'
import posthog from 'posthog-node'

import { askQuestion, confirmScriptExecution } from './common'

// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/ua-sync-posthog.js
require('dotenv').config()

const prisma = new PrismaClient()

const DATABASE_URL = process.env.DATABASE_URL
const POSTHOG_API_KEY = process.env.NEXT_PUBLIC_POSTHOG_KEY
const POSTHOG_HOST = 'https://us.i.posthog.com'

const delay = (ms) => new Promise((res) => setTimeout(res, ms))

function uaSyncPostHog() {
  return new posthog.PostHog(POSTHOG_API_KEY, {
    host: POSTHOG_HOST,
    flushAt: 1,
    flushInterval: 0,
  })
}

const main = async () => {
  try {
    const answer = await askQuestion('Do you want to proceed? (y/n): ')
    if (answer.toLowerCase() !== 'y') {
      console.log('Good bye.')
      return
    }

    const client = uaSyncPostHog()

    // Get all active memberships (communities)
    const memberships = await prisma.membership.findMany({
      where: {
        active: true,
      },
      include: {
        membership_setting: true,
      },
      orderBy: {
        id: 'desc',
      },
    })
    console.log(`Going to create PostHog Groups for ${memberships.length} communities`)

    const batchSize = 10
    const totalBatches = Math.ceil(memberships.length / batchSize)

    for (let i = 0; i < memberships.length; i += batchSize) {
      console.log(`Processing batch ${i / batchSize + 1} of ${totalBatches}`)
      const membershipsBatch = memberships.slice(i, i + batchSize)

      for (let membership of membershipsBatch) {
        console.log(`Creating PostHog Group for community: ${membership.name} (${membership.id})`)

        try {
          // Create or update a group in PostHog
          const newGroup = {
            groupType: 'community',
            groupKey: membership.id,
            properties: {
              // Membership fields
              name: membership.name,
              slug: membership.slug,
              active: membership.active,
              owner_id: membership.owner_id,

              // Membership setting fields
              main_color: membership.membership_setting?.theme_main_color || null,
              visibility: membership.membership_setting?.visibility || null,
              members_count: membership.membership_setting?.members_count || 0,
              is_paid: membership.membership_setting?.is_paid,
              is_pricing_enabled: membership.membership_setting?.is_pricing_enabled || false,
              payments_annual: membership.membership_setting?.stripe_enable_annual,

              // From membership or membership setting
              created_at: membership.createdAt.toISOString(),
              updated_at:
                membership.updatedAt > membership.membership_setting.updatedAt
                  ? membership.updatedAt.toISOString()
                  : membership.membership_setting.updatedAt.toISOString(),
            },
          }

          client.groupIdentify(newGroup)

          await delay(250) // Avoid rate limiting
        } catch (e) {
          console.error(`Error creating group for ${membership.id}:`, e)
        }
      }
    }

    console.log('Finished creating PostHog Groups for all communities')
    client.shutdown()
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main()
