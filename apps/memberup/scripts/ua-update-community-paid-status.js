// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/ua-update-community-paid-status.js

require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const main = async () => {
  try {
    console.log('Script started - Connecting to database...')

    // Find all memberships (communities) with their settings
    const memberships = await prisma.membership.findMany({
      include: {
        membership_setting: true,
      },
    })

    console.log(`Found ${memberships.length} memberships to process`)

    // Process each membership
    const updates = memberships.map((membership) => {
      return (async () => {
        // Parse the stripe_prices field if it exists
        let hasPaidPlan = false
        let currentIsPaid = membership.membership_setting?.is_paid || false

        if (membership.membership_setting?.stripe_prices) {
          try {
            // Parse the JSON string if needed
            const prices =
              typeof membership.membership_setting.stripe_prices === 'string'
                ? JSON.parse(membership.membership_setting.stripe_prices)
                : membership.membership_setting.stripe_prices

            hasPaidPlan = Array.isArray(prices) && prices.some((price) => price.active === true)

            // Log the active prices found
            if (hasPaidPlan && Array.isArray(prices)) {
              const activePrices = prices.filter((price) => price.active === true)
              console.log(`Membership ${membership.id} (${membership.name}) has ${activePrices.length} active price(s)`)
            }
          } catch (e) {
            console.error(`Error parsing stripe_prices for membership ${membership.id}:`, e)
          }
        }

        // Only update if the is_paid status would change
        if (currentIsPaid !== hasPaidPlan) {
          console.log(
            `Updating: Membership ${membership.id} (${membership.name}): is_paid from ${currentIsPaid} to ${hasPaidPlan}`,
          )

          // Actually update the database
          const result = await prisma.membershipSetting.update({
            where: {
              membership_id: membership.id,
            },
            data: {
              is_paid: hasPaidPlan,
            },
          })

          return {
            id: membership.id,
            name: membership.name,
            oldValue: currentIsPaid,
            newValue: hasPaidPlan,
            updated: true,
          }
        } else {
          return {
            id: membership.id,
            name: membership.name,
            oldValue: currentIsPaid,
            newValue: hasPaidPlan,
            updated: false,
          }
        }
      })()
    })

    console.log('Waiting for all updates to complete...')
    const results = await Promise.all(updates)

    // Summary
    const changed = results.filter((r) => r.updated).length
    console.log('--- Summary ---')
    console.log(`Total memberships: ${memberships.length}`)
    console.log(`Updated: ${changed}`)
    console.log(`Remained the same: ${memberships.length - changed}`)
    console.log('--- DONE ---')
  } catch (e) {
    console.error('Error updating memberships:', e)
    process.exit(1)
  } finally {
    console.log('Closing database connection...')
    await prisma.$disconnect()
  }
}

// Run the main function
console.log('Starting script...')
main()
  .then(() => {
    console.log('Script execution completed successfully')
  })
  .catch((error) => {
    console.error('Script execution failed:', error)
  })
