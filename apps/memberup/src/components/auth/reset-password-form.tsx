'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import Link from 'next/link'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { showToast } from '@memberup/shared/src/libs/toast'
import { resetPasswordSendEmailApi } from '@memberup/shared/src/services/apis/reset-password.api'
import { Button, Input } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { AppImg } from '@/shared-components/common/media/image'

const FormHelperTextCustom = ({ children }: { children: React.ReactNode }) => {
  return <div className="font-['Graphik'] text-[13px] font-normal text-red-500">{children}</div>
}

const ResetPasswordForm = () => {
  const resetPasswordSchema = z.object({
    email: z.string().email(),
  })

  type ResetPasswordSchemaType = z.infer<typeof resetPasswordSchema>

  const resetPasswordForm = useForm<ResetPasswordSchemaType>({
    mode: 'onBlur',
    reValidateMode: 'onSubmit',
    defaultValues: {
      email: '',
    },
    resolver: zodResolver(resetPasswordSchema),
  })

  const [emailSent, setEmailSent] = useState('')
  const [requestingPasswordReset, setRequestingPasswordReset] = useState(false)
  const [error, setError] = useState(null)

  const onPasswordResetSubmit = async (data) => {
    setRequestingPasswordReset(true)
    try {
      const res = await resetPasswordSendEmailApi(data.email)
      if (res.data.success) {
        setEmailSent(data.email)
      }
    } catch (err) {
      const errorMessage = err?.response?.data?.message || err?.message
      if (errorMessage) {
        showToast(errorMessage, 'error')
      }
    } finally {
      setRequestingPasswordReset(false)
    }
  }

  return (
    <div data-test="" className="flex flex-col items-center gap-4">
      <AppImg src="/assets/default/logos/memberup-logo.png" width={170} height={25} alt="MemberUp Logo" />
      <div className="text-white my-6 w-[296.49px] text-center font-['Graphik'] text-2xl font-semibold leading-normal">
        Reset your password
      </div>

      {emailSent && (
        <>
          <h1>We sent you an email</h1>
          <p>We’ve sent an email to {emailSent} with password reset instructions.</p>
          <p>
            Didn’t get an email,{' '}
            <a
              style={{ cursor: 'pointer' }}
              onClick={() => {
                onPasswordResetSubmit({ email: emailSent })
                setEmailSent('')
              }}
            >
              resend email
            </a>{' '}
            or check your spam folder. We sent <NAME_EMAIL>
          </p>
        </>
      )}

      {!emailSent && (
        <Form {...resetPasswordForm}>
          <div className="flex w-full flex-col space-y-5">
            <FormField
              control={resetPasswordForm.control}
              name="email"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      className="w-full"
                      autoComplete="email"
                      disabled={requestingPasswordReset}
                      type="email"
                      placeholder="Email"
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            <div className="f1t-normal font-['Graphik'] text-sm">We’ll email you a password reset link</div>
            {Boolean(error) && <FormHelperTextCustom>{error}</FormHelperTextCustom>}
            <div className="my-6">
              <Button
                className="w-full"
                loading={requestingPasswordReset}
                disabled={!resetPasswordForm.formState.isValid || requestingPasswordReset}
                onClick={resetPasswordForm.handleSubmit(onPasswordResetSubmit)}
              >
                Reset Password
              </Button>
            </div>
          </div>
        </Form>
      )}
      <div className="text-center">
        <Link href="/login">
          <span className="text-sm font-normal text-violet-600 underline">Return to login</span>
        </Link>
      </div>
    </div>
  )
}

export default ResetPasswordForm
