import { useSession } from 'next-auth/react'
import { useRouter } from 'next/router'
import { useEffect } from 'react'

import { ROUTES } from '@/memberup/settings/router'

export const useCheckRoute2 = () => {
  const { data: session, status } = useSession()
  const router = useRouter()

  // useEffect(() => {
  //     if (status === 'authenticated') {
  //         router.push('/')
  //     }
  // }, [session, status])

  //const isChatRoute = ROUTES[router.pathname]?.chatRequired || false
  const isChatRoute = false
  const isEnabledRoute = true

  return {
    session,
    status,
    isChatRoute,
    isEnabledRoute,
  }
}
