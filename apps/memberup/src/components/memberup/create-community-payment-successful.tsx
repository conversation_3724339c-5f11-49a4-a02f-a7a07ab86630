import CheckIcon from '@mui/icons-material/Check'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import Box from '@mui/material/Box'
import Button from '@mui/material/Button'
import Divider from '@mui/material/Divider'
import Grid from '@mui/material/Grid'
import Modal from '@mui/material/Modal'
import Typography from '@mui/material/Typography'
import axios from 'axios'
import Cookie from 'js-cookie'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import * as React from 'react'

import { AppImg } from '@memberup/shared/src/components/common/media/image'
// @ts-ignore
import VerifyEmailForm from '@/src/components/signup/verify-email-form'
import { getCommunityBaseURL } from '@/src/libs/utils'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN

const CreateCommunityPaymentSuccessful = ({ community }) => {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const handleContinue = () => {
    try {
      setLoading(true)
      router.push(getCommunityBaseURL(community))
    } catch (err) {
      setLoading(false)
    }
  }

  return (
    <div>
      <Modal
        open={true}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
        data-test={'verify-email-modal'}
      >
        <div className="absolute left-1/2 top-1/2 flex w-[440px] translate-x-[-50%] translate-y-[-50%] flex-col items-center justify-center rounded-2xl bg-neutral-800">
          <Box
            sx={{
              maxWidth: 990,
              m: 'auto',
              overflow: 'hidden',
            }}
          >
            <Box
              data-cy={'payment-confirmed-block'}
              sx={{
                position: 'relative',
              }}
            >
              <Box
                className="text-center"
                sx={{ position: 'relative', maxWidth: 450, m: 'auto', pt: '45px', pb: '24px' }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: 0,
                    borderBottomLeftRadius: '35%',
                    borderBottomRightRadius: '35%',
                    bottom: 0,
                    backgroundImage:
                      'linear-gradient(180deg, rgba(249, 115, 0, 1) 0%, rgba(217, 0, 135, 1) 52%, rgba(49, 0, 200, 1) 100%)',
                    filter: 'blur(160px)',
                    opacity: 0.36,
                    zIndex: 0,
                  }}
                />
                <div className={'flex justify-center'}>
                  <AppImg src={`/assets/logos/memberup-logo.png`} width={135} height={20} alt="MemberUp Logo" />
                </div>
              </Box>
            </Box>
            <Box
              sx={{
                maxWidth: 640,
                m: 'auto',
                p: '24px',
                position: 'relative',
                borderRadius: '16px',
              }}
            >
              <Box
                sx={{
                  m: 'auto',
                  mb: '16px',
                  lineHeight: 1,
                  overflow: 'hidden',
                  '& img': {
                    width: '100%',
                    height: 'auto',
                    objectFit: 'contain',
                    borderRadius: '20px',
                  },
                }}
              >
                <AppImg src={`/assets/images/dlgif.gif`} width={576} height={324} alt="DL Gif Image" />
              </Box>
              <Box sx={{ m: 'auto', mt: '24px' }}>
                <Button
                  fullWidth
                  className="round-small"
                  variant="contained"
                  sx={{ height: 48 }}
                  onClick={handleContinue}
                >
                  Continue to Community
                </Button>
              </Box>
            </Box>
          </Box>
        </div>
      </Modal>
    </div>
  )
}

CreateCommunityPaymentSuccessful.displayName = 'CreateCommunityPaymentSuccessful'

export default CreateCommunityPaymentSuccessful
