import { useState } from 'react'

import { getCommunityPricing } from '@/shared-libs/membership-settings'
import { selectMembership, selectMembershipSetting } from '@/src/store/features/membershipSlice'
import { openDialog } from '@/src/store/features/uiSlice'
import { selectUser } from '@/src/store/features/userSlice'
import { useAppDispatch } from '@/src/store/hooks'
import { useAppSelector } from '@/src/store/store'

const JoinCommunityPaymentSuccessful = () => {
  const dispatch = useAppDispatch()
  const [loading, setLoading] = useState(false)
  const user = useAppSelector((state) => selectUser(state))
  const membership = useAppSelector((state) => selectMembership(state))
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))

  let communityMonthlyPrice = getCommunityPricing(membershipSetting)
  communityMonthlyPrice = Math.round(communityMonthlyPrice.unit_amount / 100)

  const handleContinueOnClick = () => {
    dispatch(openDialog({ dialog: 'JoinCommunityCheckout', open: false }))
  }

  return (
    <div
      style={{
        maxWidth: 990,
        margin: 'auto',
        overflow: 'hidden',
      }}
    >
      <h1 className={'mb-4 text-center font-extrabold'}>Payment Successful</h1>
      <p className={'text-center text-sm'}>
        {membership.name} charged your card ${communityMonthlyPrice} and sent a receipt to {user.email}. Your card will
        be charged monthly on the date you signed up.
      </p>
      <div
        style={{
          maxWidth: 640,
          margin: 'auto',
          padding: '24px',
          position: 'relative',
          borderRadius: '16px',
        }}
      >
        <button
          className={'rounded-2x mt-8 w-full bg-violet-700 p-2 font-bold'}
          onClick={() => handleContinueOnClick()}
        >
          Enter to Community
        </button>
      </div>
    </div>
  )
}

JoinCommunityPaymentSuccessful.displayName = 'ThankYouPage'

export default JoinCommunityPaymentSuccessful
