import { Box } from '@mui/material'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogTitle from '@mui/material/DialogTitle'
import FormControl from '@mui/material/FormControl'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import ListItem from '@mui/material/ListItem'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemText from '@mui/material/ListItemText'
import Stack from '@mui/material/Stack'
import TextField from '@mui/material/TextField'
import Typography from '@mui/material/Typography'
import React, { useEffect, useState } from 'react'

import { adjustRGBA } from '@memberup/shared/src/libs/color'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGPencil from '@/memberup/components/svgs/pencil'
import SVGReportIcon from '@/memberup/components/svgs/report-icon'
import SVGTrash from '@/memberup/components/svgs/trash'
import { isUrlValid } from '@/memberup/libs/utils'

const ExternalLinkItemEditor = ({ value, onSave, onDelete }) => {
  const { theme } = useAppTheme()
  const [editable, setEditable] = useState(false)
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false)

  const [label, setLabel] = useState(value ? value.label : '')
  const [url, setUrl] = useState(value ? value.url : '')

  useEffect(() => {
    setLabel(value ? value.label : '')
    setUrl(value ? value.url : '')
  }, [value]) // Only re-run the effect if value changes

  const handleEdit = () => {
    setEditable(true)
  }

  const handleDelete = () => {
    handleOpenConfirmDialog()
  }

  const handleItemClick = () => {
    setEditable(true)
  }

  const handleOpenConfirmDialog = () => {
    setOpenConfirmDialog(true)
  }

  const handleSaveChanges = () => {
    let updatedUrl = url.trim()
    updatedUrl = updatedUrl.replace('http://', 'https://')
    if (!updatedUrl.startsWith('https://')) {
      updatedUrl = `https://${updatedUrl}`
    }
    onSave({ label, url: updatedUrl })
    setEditable(false)
  }

  const handleCancel = () => {
    setLabel(value ? value.label : '')
    setUrl(value ? value.url : '')
    setEditable(false)
  }

  const visibility = true

  const canSave = label && label.trim() !== '' && isUrlValid(url)

  return (
    <>
      {editable ? (
        <ListItem
          sx={{
            padding: '16px',
            borderRadius: '12px',
            marginBottom: '8px',
            backgroundColor: theme.palette.mode === 'dark' ? '#29292c' : '#eceeef',
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={4}>
              <FormControl error={false} className="'form-control light" fullWidth>
                <TextField
                  variant="outlined"
                  placeholder="Enter the Label"
                  value={label}
                  onChange={(e) => setLabel(e.target.value)}
                />
              </FormControl>
            </Grid>
            <Grid item xs={8}>
              <FormControl error={false} className="'form-control light" fullWidth>
                <TextField
                  placeholder="Enter the URL. Ex. https://www.memberup.com"
                  variant="outlined"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                />
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <Button
                className="app-button round-small"
                sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
                variant="contained"
                color="primary"
                disabled={!canSave}
                onClick={handleSaveChanges}
              >
                Save Changes
              </Button>
              <Button className="round-small" variant="outlined" onClick={handleCancel}>
                Cancel
              </Button>
            </Grid>
          </Grid>
        </ListItem>
      ) : value ? (
        <ListItem
          sx={{
            padding: '16px',
            borderRadius: '12px',
            marginBottom: '8px',
            backgroundColor: theme.palette.mode === 'dark' ? '#29292c' : '#eceeef',
          }}
        >
          <Stack direction="row" sx={{ width: '100%' }}>
            <Stack sx={{ flexGrow: 1 }}>
              <ListItemText
                primary={
                  <Typography
                    style={{
                      color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.87)' : '#000',
                      fontFamily: 'Graphik Medium',
                      fontSize: '14px',
                      fontWeight: 500,
                      textAlign: 'left',
                      lineHeight: '20px',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                      maxWidth: '450px',
                    }}
                  >
                    {value.label}
                  </Typography>
                }
              ></ListItemText>
              <ListItemText
                primary={
                  <Typography
                    style={{
                      opacity: 1,
                      color: theme.palette.primary.main,
                      fontFamily: 'Graphik Medium',
                      fontSize: '12px',
                      fontWeight: 500,
                      fontStyle: 'normal',
                      letterSpacing: '0px',
                      textAlign: 'left',
                      lineHeight: '16px',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                      maxWidth: '450px',
                    }}
                  >
                    {value.url}
                  </Typography>
                }
              ></ListItemText>
            </Stack>
            <IconButton
              edge="end"
              aria-label="edit"
              onClick={handleEdit}
              sx={{
                marginRight: '8px',
                '&.Mui-disabled': {
                  backgroundColor: theme.palette.mode === 'dark' ? '#323439' : '#e5e9eb',
                  padding: '0px 12px',
                  marginRight: '5px',
                  borderRadius: '20px',
                },
                '&:hover': {
                  backgroundColor: visibility ? 'transparent' : 'initial',
                },
              }}
            >
              <SVGPencil styles={{ marginRight: '3px' }} />
            </IconButton>
            <IconButton
              edge="end"
              aria-label="edit"
              onClick={handleDelete}
              sx={{
                marginRight: '8px',
                '&.Mui-disabled': {
                  backgroundColor: theme.palette.mode === 'dark' ? '#323439' : '#e5e9eb',
                  padding: '0px 12px',
                  marginRight: '5px',
                  borderRadius: '20px',
                },
                '&:hover': {
                  backgroundColor: visibility ? 'transparent' : 'initial',
                },
              }}
            >
              <SVGTrash styles={{ marginRight: '3px' }} />
            </IconButton>
          </Stack>
        </ListItem>
      ) : (
        <ListItemButton
          className="color04 font-bold"
          sx={{
            fontSize: '16px',
            padding: '16px',
            borderRadius: '12px',
            marginBottom: '8px',
            backgroundColor: theme.palette.mode === 'dark' ? '#212124' : '#e5e9eb',
          }}
          onClick={() => setEditable(true)}
        >
          <Typography
            style={{
              opacity: 1,
              color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.87)' : '#000',
              fontFamily: 'Graphik Medium',
              fontSize: '12px',
              fontWeight: 500,
              fontStyle: 'normal',
              letterSpacing: '0px',
              textAlign: 'center',
              lineHeight: '16px',
            }}
          >
            Click to create a new link
          </Typography>
        </ListItemButton>
      )}

      <Dialog
        PaperProps={{
          style: {
            borderRadius: '10px',
            backgroundColor: theme.palette.mode === 'dark' ? '#212124' : '#ffffff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            maxWidth: '456px',
          },
        }}
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <Box
          sx={{
            marginTop: '26px',
            display: 'flex',
            justifyContent: 'center',
            width: '72px',
            height: '72px',
            borderRadius: '50%',
            alignItems: 'center',
            padding: '10px',
            backgroundColor: theme.palette.mode === 'dark' ? '#2e2f33' : '#f1f2f4',
          }}
        >
          <SVGReportIcon />
        </Box>
        <DialogTitle
          sx={{
            fontFamily: 'Graphik SemiBold',
            fontSize: '18px',
            lineHeight: '24px',
            border: 'none',
            color: theme.palette.mode === 'dark' ? '#ffffff' : '#000000',
          }}
          id="alert-dialog-title"
        >
          {'Confirm Delete'}
        </DialogTitle>
        <DialogContent
          sx={{
            fontFamily: 'Graphik Regular',
            fontSize: '14px',
            textAlign: 'center',
            color: theme.palette.mode === 'dark' ? '#8D94A3' : '#212124',
            maxWidth: '70%',
            marginBottom: '0px',
          }}
        >
          Are you sure you want to delete this link?
        </DialogContent>
        <DialogActions sx={{ backgroundColor: 'transparent', marginBottom: '10px' }}>
          <Button
            sx={{
              width: '128px',
              height: '48px',
              borderRadius: '10px',
              backgroundColor: 'transparent',
              marginRight: '2px',
              border: theme.palette.mode === 'dark' ? '1px solid #ffffff' : '1px solid #000000',
              '&:hover': {
                backgroundColor: 'transparent',
              },
            }}
            className="round-small"
            variant="contained"
            onClick={() => setOpenConfirmDialog(false)}
          >
            Cancel
          </Button>
          <Button
            className="round-small"
            variant="contained"
            sx={{
              width: '128px',
              borderRadius: '10px',
              height: '48px',
              backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8),
              color: 'black',
            }}
            onClick={() => {
              onDelete()
              setOpenConfirmDialog(false)
            }}
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </>
  )
}
export default ExternalLinkItemEditor
