import Box from '@mui/material/Box'
import Modal from '@mui/material/Modal'
import * as React from 'react'

import { openDialog, selectOpenDialogs } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
// @ts-ignore
import SignUpForm from '@/src/components/signup/sign-up-form'

const style = {
  position: 'absolute' as 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
}

export default function SignUpModal() {
  const dispatch = useAppDispatch()
  const handleClose = () => {
    dispatch(openDialog({ dialog: 'SignUp', open: false }))
  }

  const handleSignUpSuccess = () => {
    dispatch(openDialog({ dialog: 'SignUp', open: false }))
  }
  const openDialogs = useAppSelector((state) => selectOpenDialogs(state))
  // @ts-ignore
  return (
    <div>
      <Modal
        open={openDialogs['SignUp']}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box className="rounded-lg" sx={style}>
          <SignUpForm onSuccess={() => handleSignUpSuccess()} />
        </Box>
      </Modal>
    </div>
  )
}
