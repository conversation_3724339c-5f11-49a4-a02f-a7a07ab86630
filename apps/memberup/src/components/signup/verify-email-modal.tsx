import * as React from 'react'
import { toast } from 'react-toastify'

import { VerifyEmailForm } from '@/components/auth/verify-email-form'
import { Modal, ModalContent } from '@/components/ui/modal'
import { resetUser, updateUser } from '@/memberup/store/features/userSlice'
import { useAppDispatch } from '@/memberup/store/hooks'

export default function VerifyEmailModal() {
  const dispatch = useAppDispatch()

  const handleVerifyEmailSuccess = (data) => {
    toast.success('Verification successful!')
    // TODO UA: Refactor into just one call
    dispatch(resetUser({ streamChatUserToken: data.streamChatUserToken, knockToken: data.knockToken }))
    dispatch(updateUser({ status: 'active' }))
  }

  return (
    <Modal open={true}>
      <ModalContent>
        <div className="absolute left-1/2 top-1/2 flex w-[440px] translate-x-[-50%] translate-y-[-50%] flex-col items-center justify-center rounded-2xl bg-neutral-800 p-12">
          <VerifyEmailForm onSuccess={(data) => handleVerifyEmailSuccess(data)} />
        </div>
      </ModalContent>
    </Modal>
  )
}
