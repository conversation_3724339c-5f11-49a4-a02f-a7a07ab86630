import * as Sentry from '@sentry/nextjs'
import { NextApiRequest } from 'next'
import getRawBody from 'raw-body'

import { getStripe } from './stripe'

export async function stripeWebhook(secretKey: string, endpointSecret: string, req: NextApiRequest) {
  try {
    const stripe = getStripe(secretKey)
    const rawBody = await getRawBody(req)
    const event = stripe.webhooks.constructEvent(rawBody, req.headers['stripe-signature'], endpointSecret)

    return event
  } catch (error) {
    console.error(error)
    Sentry.captureException(error)
    throw error
  }
}
