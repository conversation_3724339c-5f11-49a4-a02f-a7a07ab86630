import Stripe from 'stripe'

import { STRIPE_CLIENT_ID, STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import {
  STRIPE_APPLICATION_FEE_BASIC,
  STRIPE_APPLICATION_FEE_ENTERPRISE,
  STRIPE_APPLICATION_FEE_PRO,
} from '@/shared-config/envs'
import { MEMBERUP_PLAN_ENUM } from '@/shared-types/enum'
import { TStripeConnectAccount } from '@/shared-types/types'

export enum StripeEventEnum {
  PAYMENT_METHOD_ATTACHED = 'payment_method.attached',
  PAYMENT_METHOD_DETACHED = 'payment_method.detached',
  PAYMENT_METHOD_UPDATED = 'payment_method.updated',
  PAYMENT_METHOD_AUTOMATICALLY_UPDATED = 'payment_method.automatically_updated',

  CUSTOMER_SUBSCRIPTION_CREATED = 'customer.subscription.created',
  CUSTOMER_SUBSCRIPTION_UPDATED = 'customer.subscription.updated',
  CUSTOMER_SUBSCRIPTION_DELETED = 'customer.subscription.deleted',
  CUSTOMER_SUBSCRIPTION_TRIAL_WILL_END = 'customer.subscription.trial_will_end',

  INVOICE_PAID = 'invoice.paid',
  INVOICE_CREATED = 'invoice.created',
  INVOICE_PAYMENT_FAILED = 'invoice.payment_failed',

  PAYMENT_INTENT_SUCCEEDED = 'payment_intent.succeeded',
  PAYMENT_INTENT_FAILED = 'payment_intent.payment_failed',

  CUSTOMER_DELETED = 'customer.deleted',
}

export function getStripe(secretKey: string = STRIPE_SECRET_KEY) {
  return new Stripe(secretKey, {
    apiVersion: '2023-10-16',
    maxNetworkRetries: 2,
  })
}

export async function stripeCreateAccount() {
  try {
    const stripe = getStripe(STRIPE_SECRET_KEY)
    const result = await stripe.accounts.create({
      type: 'standard',
    })
    return result
  } catch (err: any) {
    console.error(err)
    throw err
  }
}

export async function stripeCreateAccountLink(stripAccountId: string, refreshUrl: string, redirectUrl: string) {
  try {
    const stripe = getStripe(STRIPE_SECRET_KEY)
    const result = await stripe.accountLinks.create({
      account: stripAccountId,
      refresh_url: refreshUrl,
      return_url: redirectUrl,
      type: 'account_onboarding',
    })
    return result
  } catch (err: any) {
    console.error(err)
    throw err
  }
}

export async function stripeGetAccount(
  liveMode: boolean,
  params: Stripe.AccountRetrieveParams,
  options: Stripe.RequestOptions,
) {
  try {
    const stripe = getStripe(STRIPE_SECRET_KEY)
    const result = await stripe.accounts.retrieve(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetAccountToken(liveMode: boolean, payload: Stripe.OAuthTokenParams) {
  try {
    const stripe = getStripe(STRIPE_SECRET_KEY)
    const result = await stripe.oauth.token(payload)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeDisconnectAccount(liveMode: boolean, stripe_user_id: string) {
  try {
    const stripe = getStripe(STRIPE_SECRET_KEY)
    const result = await stripe.oauth.deauthorize({
      client_id: STRIPE_CLIENT_ID as string,
      stripe_user_id,
    })
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeRejectAccount(liveMode: boolean, id: string) {
  try {
    const stripe = getStripe(STRIPE_SECRET_KEY)
    const result = await stripe.accounts.reject(id, { reason: 'other' })
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateCoupon(
  connectedAccountInfo: TStripeConnectAccount,
  payload: Stripe.CouponCreateParams,
  stripeAccountId?: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.coupons.create(
      payload,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeRetrieveCharge(connectedAccountInfo: TStripeConnectAccount, id?: string) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    return await stripe.charges.retrieve(id, options)
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateCouponMain(
  secretKey: string,
  payload: Stripe.CouponCreateParams,
  stripeAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.coupons.create(
      payload,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCoupon(connectedAccountInfo: TStripeConnectAccount, couponId: string) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    return await stripe.coupons.retrieve(couponId, options)
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCouponMain(secretKey: string, couponId: string, stripeAccountId?: string) {
  try {
    const stripe = getStripe(secretKey)

    const result = await stripe.coupons.retrieve(
      couponId,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCouponList(
  connectedAccountInfo: TStripeConnectAccount,
  params?: Stripe.CouponListParams,
  stripeAccountId?: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.coupons.list(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCouponListMain(
  secretKey: string,
  params?: Stripe.CouponListParams,
  stripeAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.coupons.list(params, stripeAccountId ? { stripeAccount: stripeAccountId } : undefined)
    return result
  } catch (err: any) {
    throw err
  }
}
export async function stripeUpdateCoupon(
  secretKey: string,
  couponId: string,
  payload: Stripe.CouponUpdateParams,
  stripeAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.coupons.update(
      couponId,
      payload,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}
export async function stripeDeleteCoupon(connectedAccountInfo: TStripeConnectAccount, id: string) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.coupons.del(id, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeDeletePrice(connectedAccountInfo: TStripeConnectAccount, id: string) {
  try {
    const { secretKey, options } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    return await stripe.prices.update(id, { active: false }, options)
  } catch (err: any) {
    throw err
  }
}

export async function stripeDeleteCouponMain(secretKey: string, id: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.coupons.del(id)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateCustomer(
  connectedAccountInfo: TStripeConnectAccount,
  payload: Stripe.CustomerCreateParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.create(payload, options)
    return result
  } catch (err: any) {
    throw err
  }
}

// Handles both Connect (with options) and non-Connect scenarios (add payment method) to avoid Stripe API errors
export async function stripeUpdateCustomer(
  connectedAccountInfo: TStripeConnectAccount | null,
  customerId: string,
  params: Stripe.CustomerUpdateParams,
) {
  try {
    const { options, secretKey } = connectedAccountInfo
      ? getStripeSecretKeyAndOptions(connectedAccountInfo)
      : { options: null, secretKey: STRIPE_SECRET_KEY }

    const stripe = getStripe(secretKey)

    const result = options
      ? await stripe.customers.update(customerId, params, options)
      : await stripe.customers.update(customerId, params)

    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCustomer(secretKey: string, customerId: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.retrieve(customerId)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCustomers(
  connectedAccountInfo: TStripeConnectAccount,
  params: Stripe.CustomerListParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.list(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCustomersMain(secretKey: string, params: Stripe.CustomerListParams) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.list()
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetCustomertBalanceTransactions(
  connectedAccountInfo: TStripeConnectAccount,
  customerId: string,
  params?: Stripe.BalanceTransactionListParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.listBalanceTransactions(customerId, params, options)
    return result
  } catch (err: any) {
    console.error('error ----', err)
    throw err
  }
}

export async function stripeGetCustomertBalanceTransactionsMain(
  secretKey: string,
  customerId: string,
  params?: Stripe.BalanceTransactionListParams,
  options?: Stripe.RequestOptions,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.listBalanceTransactions(customerId, params, options)
    return result
  } catch (err: any) {
    console.error('error ----', err)
    throw err
  }
}

export async function stripeCreatePromotionCode(
  connectedAccountInfo: TStripeConnectAccount,
  payload: Stripe.PromotionCodeCreateParams,
  stripeAccountId?: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.promotionCodes.create(
      payload,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreatePromotionCodeWithDiscount(
  connectedAccountInfo: TStripeConnectAccount,
  discountParams: {
    amount_off?: number
    percent_off?: number
    name?: string
    duration?: 'forever' | 'once' | 'repeating'
    duration_in_months?: number
  },
  promoCodeParams: {
    code: string
    active?: boolean
    max_redemptions?: number
  },
  stripeAccountId?: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)

    // Create coupon first (required by Stripe)
    const couponParams: Stripe.CouponCreateParams = {
      currency: 'USD',
      duration: discountParams.duration || 'once',
      name: discountParams.name,
    }

    if (discountParams.amount_off) {
      couponParams.amount_off = discountParams.amount_off
    } else if (discountParams.percent_off) {
      couponParams.percent_off = discountParams.percent_off
    }

    if (discountParams.duration === 'repeating' && discountParams.duration_in_months) {
      couponParams.duration_in_months = discountParams.duration_in_months
    }

    const coupon = await stripe.coupons.create(
      couponParams,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )

    // Create promotion code with the coupon
    const promoCodeFullParams: Stripe.PromotionCodeCreateParams = {
      coupon: coupon.id,
      code: promoCodeParams.code,
      active: promoCodeParams.active !== undefined ? promoCodeParams.active : true,
    }

    if (promoCodeParams.max_redemptions) {
      promoCodeFullParams.max_redemptions = promoCodeParams.max_redemptions
    }

    const result = await stripe.promotionCodes.create(
      promoCodeFullParams,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )

    return {
      promotion_code: result,
      coupon: coupon,
    }
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreatePromotionCodeMain(
  secretKey: string,
  payload: Stripe.PromotionCodeCreateParams,
  stripeAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.promotionCodes.create(
      payload,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreatePromotionCodeWithDiscountMain(
  secretKey: string,
  discountParams: {
    amount_off?: number
    percent_off?: number
    name?: string
    duration?: 'forever' | 'once' | 'repeating'
    duration_in_months?: number
  },
  promoCodeParams: {
    code: string
    active?: boolean
    max_redemptions?: number
  },
  stripeAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)

    // Create coupon first (required by Stripe)
    const couponParams: Stripe.CouponCreateParams = {
      currency: 'USD',
      duration: discountParams.duration || 'once',
      name: discountParams.name,
    }

    if (discountParams.amount_off) {
      couponParams.amount_off = discountParams.amount_off
    } else if (discountParams.percent_off) {
      couponParams.percent_off = discountParams.percent_off
    }

    if (discountParams.duration === 'repeating' && discountParams.duration_in_months) {
      couponParams.duration_in_months = discountParams.duration_in_months
    }

    const coupon = await stripe.coupons.create(
      couponParams,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )

    // Create promotion code with the coupon
    const promoCodeFullParams: Stripe.PromotionCodeCreateParams = {
      coupon: coupon.id,
      code: promoCodeParams.code,
      active: promoCodeParams.active !== undefined ? promoCodeParams.active : true,
    }

    if (promoCodeParams.max_redemptions) {
      promoCodeFullParams.max_redemptions = promoCodeParams.max_redemptions
    }

    const result = await stripe.promotionCodes.create(
      promoCodeFullParams,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )

    return {
      promotion_code: result,
      coupon: coupon,
    }
  } catch (err: any) {
    throw err
  }
}

export async function stripeUpdatePromotionCode(
  connectedAccountInfo: TStripeConnectAccount,
  promoId: string,
  payload: Stripe.PromotionCodeUpdateParams,
  stripeAccountId?: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.promotionCodes.update(promoId, payload, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeUpdatePromotionCodeMain(
  secretKey: string,
  promoId: string,
  payload: Stripe.PromotionCodeUpdateParams,
  stripeAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.promotionCodes.update(
      promoId,
      payload,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetPromotionCodes(
  connectedAccountInfo: TStripeConnectAccount,
  payload: Stripe.PromotionCodeListParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.promotionCodes.list(payload, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetPromotionCodesMain(
  secretKey: string,
  payload: Stripe.PromotionCodeListParams,
  stripeConnectedAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.promotionCodes.list(
      payload,
      stripeConnectedAccountId ? { stripeAccount: stripeConnectedAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateCheckoutSession(params: Stripe.Checkout.SessionCreateParams, secretKey?: string) {
  try {
    const stripe = getStripe(secretKey || STRIPE_SECRET_KEY)
    const result = await stripe.checkout.sessions.create(params)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreatePaymentIntent(
  connectedAccountInfo: TStripeConnectAccount,
  params: Stripe.PaymentIntentCreateParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentIntents.create(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreatePaymentIntentMain(secretKey: string, params: Stripe.PaymentIntentCreateParams) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentIntents.create(params)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetPaymentIntent(
  secretKey: string,
  id: string,
  params?: Stripe.PaymentIntentRetrieveParams,
  stripeAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentIntents.retrieve(
      id,
      params,
      stripeAccountId ? { stripeAccount: stripeAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateSubscription(
  connectedAccountInfo: TStripeConnectAccount,
  payload: Stripe.SubscriptionCreateParams,
  stripePriceId: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)

    const result = await stripe.subscriptions.create(
      {
        ...payload,
        items: [{ price: stripePriceId }],
        expand: ['latest_invoice.payment_intent'],
        payment_settings: { payment_method_types: ['card'] },
      },
      options,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateSubscriptionMain(
  secretKey: string,
  payload: Stripe.SubscriptionCreateParams,
  stripePriceId: string,
  stripeConnectedAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.subscriptions.create(
      {
        ...payload,
        items: [{ price: stripePriceId }],
        expand: ['latest_invoice.payment_intent'],
        payment_settings: { payment_method_types: ['card'] },
      },
      stripeConnectedAccountId ? { stripeAccount: stripeConnectedAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeAttachPaymentMethodToCustomer(
  connectedAccountInfo: TStripeConnectAccount,
  paymentMethodId: string,
  customerId: string,
  subscriptionId?: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentMethods.attach(
      paymentMethodId,
      {
        customer: customerId,
      },
      options,
    )
    await stripe.customers.update(
      customerId,
      {
        default_source: result.id,
        invoice_settings: { default_payment_method: result.id },
      },
      options,
    )

    if (subscriptionId) {
      await stripe.subscriptions.update(
        subscriptionId,
        {
          default_payment_method: paymentMethodId,
          default_source: paymentMethodId,
        },
        options,
      )
    }
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeAttachPaymentMethodToCustomerMain(
  secretKey: string,
  paymentMethodId: string,
  customerId: string,
  subscriptionId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentMethods.attach(paymentMethodId, {
      customer: customerId,
    })

    try {
      await stripe.customers.update(customerId, {
        default_source: result.id,
        invoice_settings: { default_payment_method: result.id },
      })
    } catch (e) {
      console.error(e)
    }

    if (subscriptionId) {
      try {
        await stripe.subscriptions.update(subscriptionId, {
          default_payment_method: paymentMethodId,
          default_source: paymentMethodId,
        })
      } catch (err) {
        console.error(err)
      }
    }
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetPaymentMethod(connectedAccountInfo: TStripeConnectAccount, paymentMethodId: string) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentMethods.retrieve(paymentMethodId, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetPaymentMethodMain(secretKey: string, paymentMethodId: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentMethods.retrieve(paymentMethodId)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetPaymentMethods(
  connectedAccountInfo: TStripeConnectAccount,
  customerId: string,
  params: Stripe.CustomerListPaymentMethodsParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.listPaymentMethods(customerId, params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetPaymentMethodsMain(
  secretKey: string,
  customerId: string,
  params: Stripe.CustomerListPaymentMethodsParams,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.listPaymentMethods(customerId, params)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeUpdatePaymentMethodMain(
  secretKey: string,
  id: string,
  params: Stripe.PaymentMethodUpdateParams,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentMethods.update(id, params)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeUpdatePaymentMethod(
  connectedAccountInfo: TStripeConnectAccount,
  id: string,
  params: Stripe.PaymentMethodUpdateParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentMethods.update(id, params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeDetachPaymentMethod(connectedAccountInfo: TStripeConnectAccount, id: string) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentMethods.detach(id, {}, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeConfirmPaymentIntent(
  secretKey: string,
  id: string,
  params?: Stripe.PaymentIntentConfirmParams,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.paymentIntents.confirm(id, params)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripegetRewardfulPayouts(
  connectedAccountInfo: TStripeConnectAccount,
  params?: Stripe.PayoutListParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.payouts.list(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripegetRewardfulPayoutsMain(
  secretKey: string,
  params?: Stripe.PayoutListParams,
  options?: Stripe.RequestOptions,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.payouts.list(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeDetachPaymentMethodMain(secretKey: string, paymentMethodId: string) {
  try {
    const stripe = getStripe(secretKey)
    const detached = await stripe.paymentMethods.detach(paymentMethodId)
    return detached
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetBalance(
  secretKey: string,
  params?: Stripe.BalanceRetrieveParams,
  options?: Stripe.RequestOptions,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.balance.retrieve(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetBalanceTransactions(
  connectedAccountInfo: TStripeConnectAccount,
  params?: Stripe.BalanceTransactionListParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.balanceTransactions.list(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetBalanceTransactionsMain(
  secretKey: string,
  params?: Stripe.BalanceTransactionListParams,
  options?: Stripe.RequestOptions,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.balanceTransactions.list(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetInvoices(
  params?: Stripe.InvoiceListParams,
  connectedAccountInfo?: TStripeConnectAccount,
) {
  const { secretKey, options } = getStripeSecretKeyAndOptions(connectedAccountInfo)
  const stripe = getStripe(secretKey)
  return await stripe.invoices.list(params, options)
}

// DUPLICATED?
export async function stripeGetPayments2(
  params?: Stripe.InvoiceListParams,
  connectedAccountInfo?: TStripeConnectAccount,
  options?: Stripe.RequestOptions,
) {
  try {
    let stripeSecretKey = STRIPE_SECRET_KEY
    let stripeOptions = options

    if (connectedAccountInfo) {
      const keyAndOptions = getStripeSecretKeyAndOptions(connectedAccountInfo)
      stripeSecretKey = keyAndOptions.secretKey
      stripeOptions = keyAndOptions.options
    }
    const stripe = getStripe(stripeSecretKey)
    return await stripe.paymentIntents.list(params, stripeOptions)
  } catch (err: any) {
    throw err
  }
}

// export async function stripeGetInvoices(
//   connectedAccountInfo: TStripeConnectAccount,
//   params?: Stripe.InvoiceListParams
// ) {
//   try {
//     const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
//     const stripe = getStripe(secretKey)
//     const result = await stripe.invoices.list(params, options)
//     return result
//   } catch (err: any) {
//     throw err
//   }
// }
//
// export async function stripeGetInvoicesMain(
//   secretKey: string,
//   params?: Stripe.InvoiceListParams,
//   options?: Stripe.RequestOptions
// ) {
//   try {
//     const stripe = getStripe(secretKey)
//     const result = await stripe.invoices.list(params, options)
//     return result
//   } catch (err: any) {
//     throw err
//   }
// }

export async function stripeGetInvoiceById(secretKey: string, invoiceId: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.invoices.retrieve(invoiceId)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetUpcomingInvoice(
  connectedAccountInfo: TStripeConnectAccount,
  params: Stripe.InvoiceRetrieveUpcomingParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.invoices.retrieveUpcoming(params, options)

    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetUpcomingInvoiceMain(
  secretKey: string,
  params: Stripe.InvoiceRetrieveUpcomingParams,
  stripeConnectedAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.invoices.retrieveUpcoming(
      params,
      stripeConnectedAccountId ? { stripeAccount: stripeConnectedAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeUpdateInvoice(
  secretKey: string,
  id: string,
  params: Stripe.InvoiceUpdateParams,
  stripeConnectedAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.invoices.update(
      id,
      params,
      stripeConnectedAccountId ? { stripeAccount: stripeConnectedAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripePayInvoice(secretKey: string, id: string, params?: Stripe.InvoicePayParams) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.invoices.pay(id, params)
    return result
  } catch (err: any) {
    throw err
  }
}

// export async function checkoutSession({ customer_id, customer_email }) {
//   try {
//     const payload = {
//       payment_method_types: ['card'],
//       line_items: [{ price: STRIPE_MONTHLY_PRICE_API_ID, quantity: 1 }],
//       mode: 'subscription',
//       success_url: `${NEXT_PUBLIC_HOST}/payment/checkout-success?session_id={CHECKOUT_SESSION_ID}`,
//       cancel_url: `${NEXT_PUBLIC_HOST}/payment/checkout-cancelled`,
//       allow_promotion_codes: true,
//     }

//     if (customer_id) {
//       payload.customer = customer_id
//     } else {
//       if (customer_email) {
//         payload.customer_email = customer_email
//       }

//       payload.subscription_data = {
//         trial_period_days: 7,
//       }
//     }

//     const session = await stripe.checkout.sessions.create(payload)
//     return session
//   } catch (err: any) {
//     console.error('test ======', err)
//     throw err
//   }
// }

// export async function customerBillingPortalSession({ customer_id, email }) {
//   try {
//     const payload = {
//       customer: customer_id,
//       return_url: `${NEXT_PUBLIC_HOST}/myaccount?email=${email}`,
//     }

//     const session = await stripe.billingPortal.sessions.create(payload)
//     return session
//   } catch (err: any) {
//     console.error('test ======', err)
//     throw err
//   }
// }

// export async function getSession(sessionId) {
//   try {
//     const session = await stripe.checkout.sessions.retrieve(sessionId)
//     return session
//   } catch (err: any) {
//     throw err
//   }
// }

export async function stripeGetOrCreateCustomer(
  connectedAccountInfo: TStripeConnectAccount,
  params: Stripe.CustomerCreateParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const customers = await stripe.customers.search(
      {
        query: `email:'${params.email}' AND metadata['membership_id']:'${params.metadata['membership_id']}'`,
      },
      options,
    )
    if (customers?.data?.length) {
      return customers.data[0]
    }
    const result = await stripe.customers.create(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeGetOrCreateCustomerMain(secretKey: string, params: Stripe.CustomerCreateParams) {
  try {
    const stripe = getStripe(secretKey)
    const customers = await stripe.customers.search({
      query: `email:'${params.email}' AND metadata['user_id']:'${params.metadata['user_id']}'`,
    })
    if (customers?.data?.length) {
      return customers.data[0]
    }
    const result = await stripe.customers.create(params)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeDeleteCustomer(
  secretKey: string,
  id: string,
  params?: Stripe.CustomerDeleteParams,
  stripeConnectedAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.customers.del(
      id,
      params,
      stripeConnectedAccountId ? { stripeAccount: stripeConnectedAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    console.error(err)
  }
}

function getStripeSecretKeyAndOptions(connectedAccountInfo: TStripeConnectAccount) {
  let secretKey = STRIPE_SECRET_KEY
  let options = undefined
  if (connectedAccountInfo?.access_token) {
    secretKey = connectedAccountInfo.access_token
  } else if (connectedAccountInfo?.stripe_user_id) {
    options = { stripeAccount: connectedAccountInfo.stripe_user_id }
  }
  return { secretKey, options }
}

export async function stripeCreateProduct(
  connectedAccountInfo: TStripeConnectAccount,
  params: Stripe.ProductCreateParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.products.create(params, options)
    return result
  } catch (err: any) {
    console.error('failed to create a stripe product ====', err)
    throw err
  }
}

export async function stripeGetProduct(secretKey: string, id: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.products.retrieve(id)
    return result
  } catch (err: any) {
    console.error('failed to get a stripe products ====', err)
    throw err
  }
}

export async function stripeGetProducts(
  connectedAccountInfo: TStripeConnectAccount,
  params?: Stripe.ProductListParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)

    const stripe = getStripe(secretKey)
    const result = await stripe.products.list(params, options)
    return result
  } catch (err: any) {
    console.error('failed to get stripe products ====', err)
    throw err
  }
}

export async function stripeCreatePrice(connectedAccountInfo: TStripeConnectAccount, params: Stripe.PriceCreateParams) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.prices.create(params, options)
    return result
  } catch (err: any) {
    console.error('failed to create a stripe price ====', err)
    throw err
  }
}

export async function stripeGetPrice(secretKey: string, id: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.prices.retrieve(id)
    return result
  } catch (err: any) {
    console.error('failed to get a stripe price ====', err)
    throw err
  }
}

export async function stripeGetPrices(connectedAccountInfo: TStripeConnectAccount, params: Stripe.PriceListParams) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.prices.list({ expand: ['data.product.default_price'], ...params }, options)
    return result
  } catch (err: any) {
    console.error('failed to get stripe prices ====', err)
    throw err
  }
}

export async function stripeSetProductDefaultPrice(
  connectedAccountInfo: TStripeConnectAccount,
  productId: string,
  priceId: string,
) {
  try {
    // Retrieve Stripe API credentials and initialize the Stripe client
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)

    // Update the metadata of the specified price to mark it as default
    const updatedProduct = await stripe.products.update(productId, { default_price: priceId }, options)
    return updatedProduct
  } catch (err: any) {
    console.error('Failed to set price as default ====', err)
    throw err
  }
}

export async function stripeGetPricesMain(secretKey: string, params: Stripe.PriceListParams) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.prices.list(params)
    return result
  } catch (err: any) {
    console.error('failed to get stripe prices ====', err)
    throw err
  }
}

export async function stripeUpdatePrice(
  connectedAccountInfo: TStripeConnectAccount,
  id: string,
  params: Stripe.PriceUpdateParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.prices.update(id, params, options)
    return result
  } catch (err: any) {
    console.error('failed to update a stripe price ====', err)
    throw err
  }
}

export async function stripeGetPayments(connectedAccountInfo: TStripeConnectAccount, params: any) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)

    let all = []
    let hasMore = true
    let startingAfter = null
    while (hasMore) {
      const mergedParams = {
        ...params,
        limit: 100, // Adjust the limit as needed
      }

      if (startingAfter) {
        params.starting_after = startingAfter
      }
      const result = await stripe.paymentIntents.list(mergedParams, options)
      hasMore = result.has_more
      if (result.data.length > 0) {
        startingAfter = result.data[result.data.length - 1].id
      }
      all = all.concat(result.data)
    }
    return all
  } catch (err: any) {
    console.error('failed to get stripe subscriptions ====', err)
    throw err
  }
}

export async function stripeGetSubscriptions(
  connectedAccountInfo: TStripeConnectAccount,
  params: Stripe.SubscriptionListParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.subscriptions.list(params, options)
    return result
  } catch (err: any) {
    console.error('failed to get stripe subscriptions ====', err)
    throw err
  }
}

export async function stripeGetSubscriptionsMain(secretKey: string, params: Stripe.SubscriptionListParams) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.subscriptions.list(params)
    return result
  } catch (err: any) {
    console.error('failed to get stripe subscriptions ====', err)
    throw err
  }
}

export async function stripeGetSubscription(secretKey: string, id: string, stripeConnectedAccountId?: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.subscriptions.retrieve(
      id,
      { expand: ['latest_invoice.payment_intent'] },
      stripeConnectedAccountId ? { stripeAccount: stripeConnectedAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    console.error('failed to get stripe subscription ====', err)
    throw err
  }
}

export async function stripeCancelSubscriptionMain(secretKey: string, subscriptionId: string) {
  const stripe = getStripe(secretKey)
  const result = await stripe.subscriptions.update(subscriptionId, {
    cancel_at_period_end: true,
  })
  return result
}

export async function stripeCancelSubscription(
  connectedAccountInfo: TStripeConnectAccount,
  subscriptionId: string,
  cancelAtPeriodEnd: boolean,
) {
  const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
  const stripe = getStripe(secretKey)
  const result = await stripe.subscriptions.cancel(
    subscriptionId,
    {
      prorate: false,
      invoice_now: false,
    },
    options,
  )
  return result
}

const stripeUpdateAllSubscriptions = (
  stripe: any,
  connectedAccountId: string,
  subscriptions: any[],
  newApplicationFeePercent: any,
) => {
  subscriptions.forEach((subscription) => {
    stripe.subscriptions
      .update(
        subscription.id,
        { application_fee_percent: newApplicationFeePercent },
        { stripeAccount: connectedAccountId },
      )
      .then((updatedSubscription) => {
        console.log('Updated Subscription:', {
          id: updatedSubscription.id,
          old_application_fee_percent: subscription.application_fee_percent,
          new_application_fee_percent: updatedSubscription.application_fee_percent,
        })
      })
      .catch((error) => {
        console.error('Error updating subscription:', error)
      })
  })
}

const stripeGetAllMembershipSubscriptions = async (stripe: any, connectedAccountId: string) => {
  let hasMore = true
  let lastSubscriptionId = null
  const allSubscriptions = []

  while (hasMore) {
    const params: any = { limit: 100 }
    if (lastSubscriptionId) {
      params.starting_after = lastSubscriptionId
    }

    const response = await stripe.subscriptions.list(params, { stripeAccount: connectedAccountId })

    allSubscriptions.push(...response.data)
    lastSubscriptionId = response.data.length > 0 ? response.data[response.data.length - 1].id : null
    hasMore = response.has_more
  }
  return allSubscriptions
}

export const stripeUpdateMembershipSubscriptions = async (plan: string, connectedAccountId: string) => {
  if (!connectedAccountId) {
    return
  }

  const stripe = getStripe(STRIPE_SECRET_KEY)
  const applicationFeePercent =
    plan === MEMBERUP_PLAN_ENUM.enterprise
      ? STRIPE_APPLICATION_FEE_ENTERPRISE
      : plan === MEMBERUP_PLAN_ENUM.pro
        ? STRIPE_APPLICATION_FEE_PRO
        : STRIPE_APPLICATION_FEE_BASIC

  const allSubscriptions = await stripeGetAllMembershipSubscriptions(stripe, connectedAccountId)
  stripeUpdateAllSubscriptions(stripe, connectedAccountId, allSubscriptions, applicationFeePercent)
}

export async function stripeRetrieveSubscriptionMain(secretKey: string, subscriptionId: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.subscriptions.retrieve(subscriptionId)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeUpdateSubscription(
  secretKey: string,
  subscriptionId: string,
  params: Stripe.SubscriptionUpdateParams,
) {
  try {
    const stripe = getStripe(secretKey)
    const updated = await stripe.subscriptions.update(subscriptionId, params)
    return updated
  } catch (err: any) {
    throw err
  }
}

export async function stripeUpdateMembershipSubscription(
  connectedAccountInfo: TStripeConnectAccount,
  subscriptionId: string,
  params: Stripe.SubscriptionUpdateParams,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const updated = await stripe.subscriptions.update(subscriptionId, params, options)
    return updated
  } catch (err: any) {
    throw err
  }
}

export async function stripeReportRuns(
  connectedAccountInfo: TStripeConnectAccount,
  report_type: string, // revenue_recognition.debit_credit_summary.14
  interval_start: number, // **********
  interval_end: number,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.reporting.reportRuns.create(
      {
        report_type,
        parameters: { interval_start, interval_end },
      },
      options,
    )
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeReportRunsMain(
  secretKey: string,
  report_type: string, // revenue_recognition.debit_credit_summary.14
  interval_start: number, // **********
  interval_end: number,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.reporting.reportRuns.create({
      report_type,
      parameters: { interval_start, interval_end },
    })
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeRetrieveSetupIntent(secretKey: string, setup_intent: string) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.setupIntents.retrieve(setup_intent)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateSetupIntent(
  connectedAccountInfo: TStripeConnectAccount,
  params: Stripe.SetupIntentCreateParams,
  stripeConnectedAccountId?: string,
) {
  try {
    const { options, secretKey } = getStripeSecretKeyAndOptions(connectedAccountInfo)
    const stripe = getStripe(secretKey)
    const result = await stripe.setupIntents.create(params, options)
    return result
  } catch (err: any) {
    throw err
  }
}

export async function stripeCreateSetupIntentMain(
  secretKey: string,
  params: Stripe.SetupIntentCreateParams,
  stripeConnectedAccountId?: string,
) {
  try {
    const stripe = getStripe(secretKey)
    const result = await stripe.setupIntents.create(
      params,
      stripeConnectedAccountId ? { stripeAccount: stripeConnectedAccountId } : undefined,
    )
    return result
  } catch (err: any) {
    throw err
  }
}
